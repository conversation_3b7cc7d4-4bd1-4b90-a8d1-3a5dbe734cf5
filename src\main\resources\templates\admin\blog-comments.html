<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
  <meta charset="UTF-8">
  <title>Blog Comment Moderation</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
  <link rel="stylesheet" th:href="@{/css/admin-style.css}">
  <style>
    .sort-link { text-decoration: none; color: inherit; }
    .sort-link:hover { color: #0d6efd; }
  </style>
</head>
<body>
<div th:replace="~{fragments/admin-topbar :: admin-topbar}"></div>
<div class="container-fluid">
  <div class="row">
    <div class="col-lg-3 mb-4">
      <div th:replace="~{fragments/admin-sidebar :: admin-sidebar}"></div>
    </div>
    <div class="col-lg-9">
      <main class="py-4">
        <h3 class="section-title">Blog Comment Moderation</h3>

        <div th:if="${successMessage}" class="alert alert-success alert-dismissible fade show" role="alert">
          <span th:text="${successMessage}"></span>
          <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <div th:if="${errorMessage}" class="alert alert-danger alert-dismissible fade show" role="alert">
          <span th:text="${errorMessage}"></span>
          <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>

        <div class="card">
          <div class="card-header bg-white py-3">
            <form th:action="@{/admin/blog-comments}" method="get">
              <div class="row g-2 align-items-center">
                <div class="col-md-5"><input type="text" name="search" class="form-control form-control-sm" placeholder="Search by content..." th:value="${search}"></div>
                <div class="col-md-2"><input type="date" name="startDate" class="form-control form-control-sm" th:value="${startDate}"></div>
                <div class="col-md-2"><input type="date" name="endDate" class="form-control form-control-sm" th:value="${endDate}"></div>
                <div class="col-md-3 d-flex gap-2">
                  <button type="submit" class="btn btn-sm btn-primary w-100">Filter</button>
                  <a th:href="@{/admin/blog-comments}" class="btn btn-sm btn-secondary" title="Clear"><i class="fas fa-times"></i></a>
                </div>
              </div>
            </form>
          </div>
          <div class="card-body">
            <div class="table-responsive">
              <table class="table table-hover align-middle">
                <thead>
                <tr>
                  <th>User</th>
                  <th>Comment Content</th>
                  <th>Blog Post</th>
                  <th>Date</th>
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
                </thead>
                <tbody>
                <tr th:each="comment : ${comments.content}">
                  <td th:text="${comment.user.fullName}"></td>
                  <td th:text="${#strings.abbreviate(comment.content, 80)}"></td>
                  <td th:text="${comment.blogPost != null ? #strings.abbreviate(comment.blogPost.title, 50) : 'N/A'}"></td>
                  <td th:text="${#temporals.format(comment.createdDate, 'dd-MM-yyyy HH:mm')}"></td>
                  <td><span class="badge" th:classappend="${comment.isApproved} ? 'bg-success' : 'bg-warning'" th:text="${comment.isApproved} ? 'Approved' : 'Pending'"></span></td>
                  <td>
                    <a th:href="@{/admin/moderation/comments/{id}(id=${comment.commentId})}" class="btn btn-sm btn-info" title="View Details"><i class="fas fa-eye"></i></a>
                    <form th:if="${!comment.isApproved}" th:action="@{/admin/comments/approve/{id}(id=${comment.commentId})}" method="post" class="d-inline">
                      <button type="submit" class="btn btn-sm btn-success" title="Approve"><i class="fas fa-check"></i></button>
                    </form>
                    <form th:action="@{/admin/comments/delete/{id}(id=${comment.commentId})}" method="post" onsubmit="return confirm('Delete this comment?')" class="d-inline">
                      <button type="submit" class="btn btn-sm btn-danger" title="Delete"><i class="fas fa-trash"></i></button>
                    </form>
                  </td>
                </tr>
                <tr th:if="${comments.empty}"><td colspan="6" class="text-center p-3">No comments found.</td></tr>
                </tbody>
              </table>
            </div>
          </div>
          <div class="card-footer bg-white py-3" th:if="${comments.totalPages > 1}">
            <nav>
              <ul class="pagination pagination-sm justify-content-center mb-0">
                <li class="page-item" th:classappend="${comments.first ? 'disabled' : ''}">
                  <a class="page-link" th:href="@{/admin/blog-comments(page=0, search=${search}, sortField=${sortField}, sortDir=${sortDir})}">First</a>
                </li>
                <li class="page-item" th:classappend="${!comments.hasPrevious() ? 'disabled' : ''}">
                  <a class="page-link" th:href="@{/admin/blog-comments(page=${comments.number - 1}, search=${search}, sortField=${sortField}, sortDir=${sortDir})}">Previous</a>
                </li>
                <li class="page-item" th:each="i : ${#numbers.sequence(0, comments.totalPages - 1)}" th:classappend="${i == comments.number ? 'active' : ''}">
                  <a class="page-link" th:href="@{/admin/blog-comments(page=${i}, search=${search}, sortField=${sortField}, sortDir=${sortDir})}" th:text="${i + 1}"></a>
                </li>
                <li class="page-item" th:classappend="${!comments.hasNext() ? 'disabled' : ''}">
                  <a class="page-link" th:href="@{/admin/blog-comments(page=${comments.number + 1}, search=${search}, sortField=${sortField}, sortDir=${sortDir})}">Next</a>
                </li>
                <li class="page-item" th:classappend="${comments.last ? 'disabled' : ''}">
                  <a class="page-link" th:href="@{/admin/blog-comments(page=${comments.totalPages - 1}, search=${search}, sortField=${sortField}, sortDir=${sortDir})}">Last</a>
                </li>
              </ul>
            </nav>
          </div>
        </div>
      </main>
    </div>
  </div>
</div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>