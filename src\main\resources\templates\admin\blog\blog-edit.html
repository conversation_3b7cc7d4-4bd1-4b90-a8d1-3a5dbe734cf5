<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
  <meta charset="UTF-8">
  <title>Admin - Edit Blog Post</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
  <link rel="stylesheet" th:href="@{/css/admin-style.css}">

</head>
<body>
<div th:replace="~{fragments/admin-topbar :: admin-topbar}"></div>

<div class="container-fluid">
  <div class="row">
    <div class="col-lg-3 mb-4">
      <div th:replace="~{fragments/admin-sidebar :: admin-sidebar}"></div>
    </div>

    <div class="col-lg-9">
      <main class="py-4">
        <h3 class="section-title">Edit Blog Post</h3>

        <div class="card">
          <div class="card-body">
            <form th:action="@{/admin/blogs/edit/{id}(id=${blog.blogId})}" th:object="${blog}" method="post">
              <div class="mb-3">
                <label for="title" class="form-label">Post Title</label>
                <input type="text" class="form-control" id="title" th:field="*{title}" required>
              </div>

              <div class="mb-3">
                <label for="content" class="form-label">Content</label>
                <textarea class="form-control" id="content" th:field="*{content}" rows="15"></textarea>
              </div>

              <div class="text-end">
                <a th:href="@{/admin/blogs}" class="btn btn-secondary me-2">Cancel</a>
                <button type="submit" class="btn btn-primary">Save Changes</button>
              </div>
            </form>
          </div>
        </div>
      </main>
    </div>
  </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>