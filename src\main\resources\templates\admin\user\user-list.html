<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Management - ReadHub Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Lora:wght@400;700&family=Montserrat:wght@400;700&family=Open+Sans:wght@400;600&display=swap" rel="stylesheet">

    <link rel="stylesheet" th:href="@{/css/admin-layout.css}">

</head>
<body>

<div th:replace="~{fragments/admin-topbar :: admin-topbar}"></div>

<div class="container my-4">
    <div class="row">
        <div class="col-lg-3 mb-4">
            <div th:replace="~{fragments/admin-sidebar :: admin-sidebar}"></div>
        </div>

        <div class="col-lg-9">
            <main class="py-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h3 class="section-title">User Management</h3>
                </div>

                <div class="card">
                    <div class="card-header bg-white py-3">
                        <form th:action="@{/admin/users}" method="get" class="row g-3 align-items-center">
                            <div class="col-md-5">
                                <input class="form-control" type="search" name="keyword" placeholder="Search by name or email..." th:value="${keyword}">
                            </div>
                            <div class="col-md-3">
                                <select name="role" class="form-select">
                                    <option value="">All Roles</option>
                                    <option value="BUYER" th:selected="${roleFilter == 'BUYER'}">Buyer</option>
                                    <option value="SELLER" th:selected="${roleFilter == 'SELLER'}">Seller</option>
                                    <option value="ADMIN" th:selected="${roleFilter == 'ADMIN'}">Admin</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select name="status" class="form-select">
                                    <option value="">All Status</option>
                                    <option value="active" th:selected="${statusFilter == 'active'}">Active</option>
                                    <option value="inactive" th:selected="${statusFilter == 'inactive'}">Blocked</option>
                                </select>
                            </div>
                            <div class="col-md-2 d-flex gap-2">
                                <button class="btn btn-outline-primary w-100" type="submit">Filter</button>
                                <a th:href="@{/admin/users}" class="btn btn-outline-secondary" title="Clear Filters">
                                    <i class="fas fa-times"></i>
                                </a>
                            </div>
                        </form>
                    </div>

                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover align-middle">
                                <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Role(s)</th>
                                    <th>Date of Birth</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr th:if="${userPage.empty}">
                                    <td colspan="7" class="text-center p-4">No users found.</td>
                                </tr>
                                <tr th:each="user : ${userPage.content}">
                                    <td th:text="${user.userId}"></td>
                                    <td th:text="${user.fullName}"></td>
                                    <td th:text="${user.email}"></td>
                                    <td>
                                        <span th:each="userRole : ${user.userRoles}"
                                              th:text="${userRole.role.roleName}"
                                              class="badge me-1"
                                              th:classappend="${userRole.role.roleName == 'ADMIN' ? 'bg-danger' : (userRole.role.roleName == 'SELLER' ? 'bg-success' : 'bg-primary')}">
                                        </span>
                                    </td>
                                    <td th:text="${#temporals.format(user.dateOfBirth, 'dd-MM-yyyy')}"></td>
                                    <td>
                                        <span class="badge" th:classappend="${user.isActive ? 'bg-success' : 'bg-secondary'}"
                                              th:text="${user.isActive ? 'Active' : 'Blocked'}"></span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a th:href="@{'/admin/users/details/' + ${user.userId}}" class="btn btn-outline-secondary" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a th:href="@{'/admin/users/edit/' + ${user.userId}}" class="btn btn-outline-primary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div class="card-footer bg-white py-3" th:if="${totalPages > 0}">
                        <div class="row align-items-center">
                            <div class="col-md-6 text-muted small">
                                Showing page <strong th:text="${currentPage}"></strong> of <strong th:text="${totalPages}"></strong>
                                (<span th:text="${totalItems}"></span> total users)
                            </div>
                            <div class="col-md-6">
                                <nav class="float-md-end" th:if="${totalPages > 1}">
                                    <ul class="pagination pagination-sm mb-0">
                                        <li class="page-item" th:classappend="${currentPage == 1 ? 'disabled' : ''}">
                                            <a class="page-link" th:href="@{/admin/users(page=${currentPage - 1}, keyword=${keyword}, role=${roleFilter}, status=${statusFilter})}">Previous</a>
                                        </li>
                                        <li class="page-item" th:each="i : ${#numbers.sequence(1, totalPages)}" th:classappend="${currentPage == i ? 'active' : ''}">
                                            <a class="page-link" th:href="@{/admin/users(page=${i}, keyword=${keyword}, role=${roleFilter}, status=${statusFilter})}" th:text="${i}"></a>
                                        </li>
                                        <li class="page-item" th:classappend="${currentPage == totalPages ? 'disabled' : ''}">
                                            <a class="page-link" th:href="@{/admin/users(page=${currentPage + 1}, keyword=${keyword}, role=${roleFilter}, status=${statusFilter})}">Next</a>
                                        </li>
                                    </ul>
                                </nav>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>