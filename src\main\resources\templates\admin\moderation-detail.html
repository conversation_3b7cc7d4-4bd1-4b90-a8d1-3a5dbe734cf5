<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>Moderation Detail</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link rel="stylesheet" th:href="@{/css/admin-style.css}">
</head>
<body>
<div th:replace="~{fragments/admin-topbar :: admin-topbar}"></div>
<div class="container-fluid">
    <div class="row">
        <div class="col-lg-3 mb-4">
            <div th:replace="~{fragments/admin-sidebar :: admin-sidebar}"></div>
        </div>
        <div class="col-lg-9">
            <main class="py-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h3 class="section-title" th:text="${contentType == 'review'} ? 'Review Detail' : 'Comment Detail'"></h3>
                    <a th:href="${contentType == 'review' ? '/admin/product-reviews' : '/admin/blog-comments'}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to List
                    </a>
                </div>

                <div th:if="${contentType == 'review'}" class="card">
                    <div class="card-body">
                        <h5 class="card-title" th:text="${review.title}"></h5>
                        <div class="mb-3">
                            <span th:each="i : ${#numbers.sequence(1, review.rating)}" class="text-warning"><i class="fas fa-star"></i></span>
                        </div>
                        <p class="card-text" style="white-space: pre-wrap;" th:text="${review.content}"></p>
                        <hr>
                        <p class="mb-1"><strong>User:</strong> <span th:text="${review.user.fullName}"></span></p>
                        <p class="mb-1"><strong>Book:</strong> <span th:text="${review.orderItem?.book?.title ?: 'N/A'}"></span></p>
                        <p class="mb-1"><strong>Date:</strong> <span th:text="${#temporals.format(review.createdDate, 'dd-MM-yyyy HH:mm')}"></span></p>
                        <p class="mb-3"><strong>Status:</strong> <span class="badge" th:classappend="${review.isApproved} ? 'bg-success' : 'bg-warning'" th:text="${review.isApproved} ? 'Approved' : 'Pending'"></span></p>

                        <div class="actions">
                            <form th:if="${!review.isApproved}" th:action="@{/admin/reviews/approve/{id}(id=${review.reviewId})}" method="post" class="d-inline me-2">
                                <button type="submit" class="btn btn-success"><i class="fas fa-check me-2"></i>Approve</button>
                            </form>
                            <form th:action="@{/admin/reviews/delete/{id}(id=${review.reviewId})}" method="post" onsubmit="return confirm('Delete this review?')" class="d-inline">
                                <button type="submit" class="btn btn-danger"><i class="fas fa-trash me-2"></i>Delete</button>
                            </form>
                        </div>
                    </div>
                </div>

                <div th:if="${contentType == 'comment'}" class="card">
                    <div class="card-body">
                        <p class="card-text" style="white-space: pre-wrap;" th:text="${comment.content}"></p>
                        <hr>
                        <p class="mb-1"><strong>User:</strong> <span th:text="${comment.user.fullName}"></span></p>
                        <p class="mb-1"><strong>Blog Post:</strong> <span th:text="${comment.blogPost?.title ?: 'N/A'}"></span></p>
                        <p class="mb-1"><strong>Date:</strong> <span th:text="${#temporals.format(comment.createdDate, 'dd-MM-yyyy HH:mm')}"></span></p>
                        <p class="mb-3"><strong>Status:</strong> <span class="badge" th:classappend="${comment.isApproved} ? 'bg-success' : 'bg-warning'" th:text="${comment.isApproved} ? 'Approved' : 'Pending'"></span></p>

                        <div class="actions">
                            <form th:if="${!comment.isApproved}" th:action="@{/admin/comments/approve/{id}(id=${comment.commentId})}" method="post" class="d-inline me-2">
                                <button type="submit" class="btn btn-success"><i class="fas fa-check me-2"></i>Approve</button>
                            </form>
                            <form th:action="@{/admin/comments/delete/{id}(id=${comment.commentId})}" method="post" onsubmit="return confirm('Delete this comment?')" class="d-inline">
                                <button type="submit" class="btn btn-danger"><i class="fas fa-trash me-2"></i>Delete</button>
                            </form>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>