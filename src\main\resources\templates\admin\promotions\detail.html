<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="'Promotion: ' + (${promotion.name} ?: 'Unknown') + ' - ReadHub Admin'">Promotion Details - ReadHub Admin</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Lora:wght@400;700&family=Montserrat:wght@400;700&family=Open+Sans:wght@400;600&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Open Sans', sans-serif;
            background-color: #F8F5F0;
            color: #333;
        }
        .navbar-brand {
            font-family: 'Lora', serif;
            font-weight: 700;
            font-size: 1.8rem;
            color: #2C3E50;
        }
        .section-title {
            font-family: 'Montserrat', sans-serif;
            font-weight: 700;
            margin-bottom: 1.5rem;
            color: #2C3E50;
            border-bottom: 2px solid #2C3E50;
            padding-bottom: 0.5rem;
        }
        .btn-primary {
            background-color: #2C3E50;
            border-color: #2C3E50;
        }
        .btn-primary:hover {
            background-color: #1e2b37;
            border-color: #1e2b37;
        }
        .btn-outline-primary {
            color: #2C3E50;
            border-color: #2C3E50;
        }
        .btn-outline-primary:hover {
            background-color: #2C3E50;
            border-color: #2C3E50;
        }
        .card {
            border: none;
            border-radius: 8px;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
        }
        .detail-section {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        .detail-section-title {
            color: #2C3E50;
            font-weight: 600;
            margin-bottom: 15px;
            border-bottom: 2px solid #dee2e6;
            padding-bottom: 8px;
        }
        .status-badge {
            font-size: 0.9rem;
        }
        .usage-progress {
            height: 8px;
        }
        .metric-card {
            background: linear-gradient(135deg, #2C3E50 0%, #1e2b37 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
        }
        .scope-item {
            background-color: #e9ecef;
            border-radius: 5px;
            padding: 8px 12px;
            margin: 5px;
            display: inline-block;
        }
        .profile-image {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .default-profile {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background-color: #E9ECEF;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: #6c757d;
            border: 3px solid #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <!-- Include Topbar -->
    <div th:replace="~{fragments/admin-topbar :: admin-topbar}"></div>

    <div class="container-fluid">
        <div class="row">
            <!-- Include Sidebar -->
            <div class="col-lg-3 mb-4">
                <div th:replace="~{fragments/admin-sidebar :: admin-sidebar}"></div>
            </div>

            <div class="col-lg-9">
                <main class="py-4">
                    <!-- Check if promotion exists -->
                    <div th:if="${promotion == null}" class="alert alert-danger">
                        <h4>Promotion Not Found</h4>
                        <p>The requested promotion could not be found.</p>
                        <a href="/admin/promotions" class="btn btn-primary">Back to Promotions</a>
                    </div>

                    <!-- Promotion Details (only show if promotion exists) -->
                    <div th:if="${promotion != null}">
                        <!-- Header -->
                        <div class="d-flex justify-content-between align-items-center mb-4">
                        <div>
                            <h3 class="section-title" th:text="${promotion.name ?: 'Unknown Promotion'}">Promotion Name</h3>
                            <p class="text-muted">
                                Code: <strong th:text="${promotion.code ?: 'NO_CODE'}">SAVE20</strong>
                                <span class="badge ms-2"
                                      th:class="${(promotion.isActive ?: false) ? 'bg-success' : 'bg-secondary'}"
                                      th:text="${promotion.status != null ? promotion.status.displayName : 'Unknown'}">Active</span>
                            </p>
                        </div>
                        <div>
                            <a th:if="${!promotion.isActive and promotion.isNeverUsed()}"
                               th:href="@{/admin/promotions/{id}/edit(id=${promotion.promotionId})}"
                               class="btn btn-primary me-2">
                                <i class="fas fa-edit"></i> Edit
                            </a>
                            <form th:action="@{/admin/promotions/{id}/toggle-status(id=${promotion.promotionId})}"
                                  method="post" style="display: inline;">
                                <button type="submit" class="btn me-2"
                                        th:class="${(promotion.isActive ?: false) ? 'btn-warning' : 'btn-success'}"
                                        th:text="${(promotion.isActive ?: false) ? 'Deactivate' : 'Activate'}">
                                    <i class="fas" th:class="${(promotion.isActive ?: false) ? 'fa-pause' : 'fa-play'}"></i>
                                </button>
                            </form>
                            <a href="/admin/promotions" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left"></i> Back to List
                            </a>
                        </div>
                    </div>

        <!-- Usage Metrics -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="metric-value" th:text="${promotion.currentUsageCount ?: 0}">0</div>
                            <div>Total Uses</div>
                        </div>
                        <i class="fas fa-chart-line fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
            <div class="col-md-3" th:if="${promotion.totalUsageLimit != null}">
                <div class="metric-card">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="metric-value" th:text="${promotion.usagePercentage != null ? (#numbers.formatDecimal(promotion.usagePercentage, 1, 1) + '%') : '0%'}">0%</div>
                            <div>Usage Rate</div>
                        </div>
                        <i class="fas fa-percentage fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="metric-value">
                                <span th:if="${promotion.promotionType != null and promotion.promotionType.name() == 'PERCENTAGE_DISCOUNT'}"
                                      th:text="${promotion.discountValue != null ? (promotion.discountValue + '%') : '0%'}">10%</span>
                                <span th:unless="${promotion.promotionType != null and promotion.promotionType.name() == 'PERCENTAGE_DISCOUNT'}"
                                      th:text="${promotion.discountValue != null ? (#numbers.formatDecimal(promotion.discountValue, 1, 0) + ' VNĐ') : '0 VNĐ'}">10 VNĐ</span>
                            </div>
                            <div>Discount Value</div>
                        </div>
                        <i class="fas fa-tags fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="metric-value" th:text="${promotion.endDate != null ? #temporals.format(promotion.endDate, 'MMM dd') : 'No End Date'}">Dec 31</div>
                            <div>Expires</div>
                        </div>
                        <i class="fas fa-calendar fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Left Column -->
            <div class="col-lg-8">
                    <!-- Basic Information -->
                    <div class="detail-section">
                        <h5 class="detail-section-title">
                            <i class="fas fa-info-circle"></i> Basic Information
                        </h5>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Promotion Name</label>
                                <div th:text="${promotion.name ?: 'Unknown Promotion'}">Summer Sale 2024</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Promotion Code</label>
                                <div>
                                    <code th:text="${promotion.code ?: 'NO_CODE'}">SUMMER2024</code>
                                    <button class="btn btn-sm btn-outline-secondary ms-2"
                                            onclick="copyToClipboard(this)"
                                            th:data-text="${promotion.code ?: 'NO_CODE'}">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row" th:if="${promotion.description != null and !promotion.description.isEmpty()}">
                        <div class="col-12">
                            <div class="mb-3">
                                <label class="form-label text-muted">Description</label>
                                <div th:text="${promotion.description ?: 'No description available'}">Promotion description</div>
                            </div>
                        </div>
                    </div>
                </div>

                    <!-- Discount Configuration -->
                    <div class="detail-section">
                        <h5 class="detail-section-title">
                            <i class="fas fa-percentage"></i> Discount Configuration
                        </h5>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label text-muted">Type</label>
                                <div th:text="${promotion.promotionType != null ? promotion.promotionType.displayName : 'Unknown'}">Percentage Discount</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label text-muted">Discount Value</label>
                                <div>
                                    <span th:if="${promotion.promotionType != null and promotion.promotionType.name() == 'PERCENTAGE_DISCOUNT'}"
                                          th:text="${promotion.discountValue != null ? (promotion.discountValue + '%') : '0%'}">10%</span>
                                    <span th:unless="${promotion.promotionType != null and promotion.promotionType.name() == 'PERCENTAGE_DISCOUNT'}"
                                          th:text="${promotion.discountValue != null ? (#numbers.formatDecimal(promotion.discountValue, 1, 0) + ' VNĐ') : '0 VNĐ'}">10 VNĐ</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4" th:if="${promotion.maxDiscountAmount != null}">
                            <div class="mb-3">
                                <label class="form-label text-muted">Max Discount</label>
                                <div th:text="${promotion.maxDiscountAmount != null ? (#numbers.formatDecimal(promotion.maxDiscountAmount, 1, 0) + ' VNĐ') : 'No Limit'}">100 VNĐ</div>
                            </div>
                        </div>
                    </div>
                    <div class="row" th:if="${promotion.minOrderValue != null}">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Minimum Order Value</label>
                                <div th:text="${promotion.minOrderValue != null ? (#numbers.formatDecimal(promotion.minOrderValue, 1, 0) + ' VNĐ') : 'No Minimum'}">50 VNĐ</div>
                            </div>
                        </div>
                    </div>
                </div>

                    <!-- Scope Configuration -->
                    <div class="detail-section">
                        <h5 class="detail-section-title">
                            <i class="fas fa-target"></i> Scope Configuration
                        </h5>
                    <div class="mb-3">
                        <label class="form-label text-muted">Scope Type</label>
                        <div>
                            <span class="badge bg-info status-badge" th:text="${promotion.scopeType != null ? promotion.scopeType.displayName : 'Unknown'}">Site-wide</span>
                        </div>
                    </div>

                    <!-- Category Scope -->
                    <div th:if="${promotion.scopeType != null and promotion.scopeType.name() == 'CATEGORY' and promotion.applicableCategories != null and !promotion.applicableCategories.empty}">
                        <label class="form-label text-muted">Applicable Categories</label>
                        <div>
                            <span th:each="category : ${promotion.applicableCategories}"
                                  class="scope-item" th:text="${category.categoryName ?: 'Unknown Category'}">Category</span>
                        </div>
                    </div>

                    <!-- Product and Shop scopes removed in simplified system -->
                </div>

                    <!-- Time Configuration -->
                    <div class="detail-section">
                        <h5 class="detail-section-title">
                            <i class="fas fa-calendar"></i> Time Configuration
                        </h5>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Start Date & Time</label>
                                <div th:text="${promotion.startDate != null ? #temporals.format(promotion.startDate, 'MMM dd, yyyy HH:mm') : 'No Start Date'}">Jan 01, 2024 00:00</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">End Date & Time</label>
                                <div th:text="${promotion.endDate != null ? #temporals.format(promotion.endDate, 'MMM dd, yyyy HH:mm') : 'No End Date'}">Dec 31, 2024 23:59</div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <div class="mb-3">
                                <label class="form-label text-muted">Status</label>
                                <div>
                                    <span th:if="${promotion != null and promotion.isNotStarted()}" class="badge bg-warning">Not Started</span>
                                    <span th:if="${promotion != null and promotion.canBeUsed()}" class="badge bg-success">Active</span>
                                    <span th:if="${promotion != null and promotion.isExpired()}" class="badge bg-danger">Expired</span>
                                    <span th:if="${promotion != null and promotion.isUsedUp()}" class="badge bg-secondary">Used Up</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Column -->
            <div class="col-lg-4">
                <!-- Usage Limits -->
                <div class="detail-section">
                    <h5 class="detail-section-title">
                        <i class="fas fa-chart-bar"></i> Usage Statistics
                    </h5>
                    <div class="mb-3">
                        <label class="form-label text-muted">Current Usage</label>
                        <div class="h4" th:text="${promotion.currentUsageCount ?: 0}">0</div>
                    </div>
                    
                    <div class="mb-3" th:if="${promotion.usageLimitPerUser != null}">
                        <label class="form-label text-muted">Per User Limit</label>
                        <div th:text="${promotion.usageLimitPerUser}">1</div>
                    </div>
                    
                    <div class="mb-3" th:if="${promotion.totalUsageLimit != null}">
                        <label class="form-label text-muted">Total Usage Limit</label>
                        <div th:text="${promotion.totalUsageLimit}">100</div>
                        
                        <div class="mt-2">
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <small class="text-muted">Progress</small>
                                <small th:text="${promotion.usagePercentage != null ? (#numbers.formatDecimal(promotion.usagePercentage, 1, 1) + '%') : '0%'}">50%</small>
                            </div>
                            <div class="progress usage-progress">
                                <div class="progress-bar"
                                     th:style="'width: ' + ${promotion.usagePercentage ?: 0} + '%'"
                                     th:class="${(promotion.usagePercentage ?: 0) > 80 ? 'bg-warning' : 'bg-primary'}"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Audit Information -->
                <div class="detail-section">
                    <h5 class="detail-section-title">
                        <i class="fas fa-history"></i> Audit Information
                    </h5>
                    <div class="mb-3">
                        <label class="form-label text-muted">Created By</label>
                        <div th:text="${promotion.createdBy?.email ?: 'System'}">admin</div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label text-muted">Created At</label>
                        <div th:text="${promotion.createdAt != null ? #temporals.format(promotion.createdAt, 'MMM dd, yyyy HH:mm') : 'Unknown'}">Jan 01, 2024 10:00</div>
                    </div>
                    <div class="mb-3" th:if="${promotion.updatedAt != null}">
                        <label class="form-label text-muted">Last Updated</label>
                        <div th:text="${promotion.updatedAt != null ? #temporals.format(promotion.updatedAt, 'MMM dd, yyyy HH:mm') : 'Never'}">Jan 02, 2024 15:30</div>
                    </div>
                    <div class="mb-3" th:if="${promotion.updatedBy != null}">
                        <label class="form-label text-muted">Updated By</label>
                        <div th:text="${promotion.updatedBy != null ? promotion.updatedBy.email : 'Unknown'}">admin</div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="detail-section">
                    <h5 class="detail-section-title">
                        <i class="fas fa-bolt"></i> Quick Actions
                    </h5>
                    <div class="d-grid gap-2">
                        <a th:if="${!promotion.isActive and promotion.isNeverUsed()}"
                           th:href="@{/admin/promotions/{id}/edit(id=${promotion.promotionId})}"
                           class="btn btn-primary">
                            <i class="fas fa-edit"></i> Edit Promotion
                        </a>
                        <form th:action="@{/admin/promotions/{id}/toggle-status(id=${promotion.promotionId})}" 
                              method="post">
                            <button type="submit" class="btn w-100"
                                    th:class="${(promotion.isActive ?: false) ? 'btn-warning' : 'btn-success'}"
                                    th:text="${(promotion.isActive ?: false) ? 'Deactivate' : 'Activate'}">
                                <i class="fas" th:class="${(promotion.isActive ?: false) ? 'fa-pause' : 'fa-play'}"></i>
                            </button>
                        </form>
                        <form th:action="@{/admin/promotions/{id}/delete(id=${promotion.promotionId})}" 
                              method="post" onsubmit="return confirm('Are you sure you want to delete this promotion?')">
                            <button type="submit" class="btn btn-outline-danger w-100">
                                <i class="fas fa-trash"></i> Delete
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
                    </div> <!-- End promotion details check -->
                </main>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <div th:if="${success}" class="toast-container position-fixed bottom-0 end-0 p-3">
        <div class="toast show" role="alert">
            <div class="toast-header bg-success text-white">
                <i class="fas fa-check-circle me-2"></i>
                <strong class="me-auto">Success</strong>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body" th:text="${success}">Success message</div>
        </div>
    </div>

    <div th:if="${error}" class="toast-container position-fixed bottom-0 end-0 p-3">
        <div class="toast show" role="alert">
            <div class="toast-header bg-danger text-white">
                <i class="fas fa-exclamation-circle me-2"></i>
                <strong class="me-auto">Error</strong>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body" th:text="${error}">Error message</div>
        </div>
    </div>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function copyToClipboard(button) {
            const text = button.getAttribute('data-text');
            navigator.clipboard.writeText(text).then(function() {
                const icon = button.querySelector('i');
                icon.className = 'fas fa-check';
                button.classList.add('btn-success');
                button.classList.remove('btn-outline-secondary');
                
                setTimeout(function() {
                    icon.className = 'fas fa-copy';
                    button.classList.remove('btn-success');
                    button.classList.add('btn-outline-secondary');
                }, 2000);
            });
        }
    </script>
</body>
</html>
