<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>Book Details</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link rel="stylesheet" th:href="@{/css/admin-style.css}">

    <style>
        body { font-family: 'Open Sans', sans-serif; background-color: #F8F5F0; color: #333; }
        .navbar-brand { font-family: 'Lora', serif; font-weight: 700; font-size: 1.8rem; color: #2C3E50; }
        .section-title { font-family: 'Montserrat', sans-serif; font-weight: 700; color: #2C3E50; }
        .card { border: none; border-radius: 8px; box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08); }
        .btn-primary { background-color: #2C3E50; border-color: #2C3E50; }
        .btn-primary:hover { background-color: #1e2b37; border-color: #1e2b37; }
    </style>
</head>
<body class="admin-body">

<div th:replace="~{fragments/admin-topbar :: admin-topbar}"></div>

<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-lg-3 mb-4">
            <div th:replace="~{fragments/admin-sidebar :: admin-sidebar}"></div>
        </div>

        <div class="col-lg-9">
            <main th:if="${book != null}">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2 class="section-title mb-0">Book Details</h2>
                    <div>
                        <a th:href="@{/admin/products}" class="btn btn-secondary"><i class="fas fa-arrow-left me-2"></i>Back to List</a>
                        <a th:href="@{'/admin/products/edit/' + ${book.bookId}}" class="btn btn-primary"><i class="fas fa-edit me-2"></i>Edit Book</a>
                    </div>
                </div>

                <div class="row">
                    <div class="col-lg-4">
                        <div class="card shadow-sm mb-4 text-center">
                            <img th:src="${book.coverImgUrl != null and !book.coverImgUrl.isEmpty() ? book.coverImgUrl : '/images/default-book.png'}"
                                 alt="Book Cover" class="card-img-top">

                            <div class="card-body">
                            </div>
                                <h3 class="card-title text-danger" th:text="${#numbers.formatDecimal(book.sellingPrice, 0, 'COMMA', 0, 'POINT')} + ' ₫'"></h3>
                                <p class="card-text text-muted">
                                    <span th:text="${book.stockQuantity}"></span> items in stock
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-8">
                        <div class="card shadow-sm">
                            <div class="card-body p-4">
                                <h2 class="mb-2" th:text="${book.title}"></h2>
                                <h5 class="text-muted mb-3" th:text="'by ' + ${book.authors}"></h5>
                                <hr>
                                <dl class="row">
                                    <dt class="col-sm-3">ISBN</dt><dd class="col-sm-9" th:text="${book.isbn ?: 'N/A'}"></dd>
                                    <dt class="col-sm-3">Publisher</dt><dd class="col-sm-9" th:text="${book.publisher?.publisherName ?: 'N/A'}"></dd>
                                    <dt class="col-sm-3">Publication Date</dt><dd class="col-sm-9" th:text="${book.publicationDate != null ? #temporals.format(book.publicationDate, 'dd-MM-yyyy') : 'N/A'}"></dd>
                                    <dt class="col-sm-3">Categories</dt>
                                    <dd class="col-sm-9">
                                        <span th:each="cat : ${book.categories}" class="badge bg-secondary me-1" th:text="${cat.categoryName}"></span>
                                    </dd>
                                </dl>
                                <hr>
                                <h5>Description</h5>
                                <p style="white-space: pre-wrap;" th:text="${book.description}"></p>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>