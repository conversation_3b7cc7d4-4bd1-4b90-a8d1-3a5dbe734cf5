<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
  <meta charset="UTF-8">
  <title>Manage Reviews - ReadHub Seller</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
  <link rel="stylesheet" th:href="@{/css/seller-style.css}">
  <style>
    .product-review-card { transition: transform .2s, box-shadow .2s; height: 100%; }
    .product-review-card:hover { transform: translateY(-5px); box-shadow: 0 1rem 3rem rgba(0,0,0,.175)!important; }
    .product-cover { width: 100%; height: 200px; object-fit: cover; }
  </style>
</head>
<body>
<div th:replace="~{fragments/seller-topbar :: seller-topbar}"></div>
<div class="container-fluid">
  <div class="row">
    <div class="col-lg-3 mb-4">
      <div th:replace="~{fragments/seller-sidebar :: seller-sidebar}"></div>
    </div>
    <main class="col-lg-9 py-4">
      <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">Product Reviews</h1>
      </div>

      <form th:action="@{/seller/reviews}" method="get" class="card card-body mb-4">
        <div class="row g-3">
          <div class="col-md-6">
            <label for="searchTitle" class="form-label">Search by Book Title</label>
            <input type="text" class="form-control" id="searchTitle" name="searchTitle" th:value="${searchTitle}" placeholder="Enter book title...">
          </div>
          <div class="col-md-6">
            <label for="sort" class="form-label">Sort By</label>
            <select id="sort" name="sort" class="form-select">
              <option value="date_desc" th:selected="${sort == 'date_desc'}">Newest First</option>
              <option value="date_asc" th:selected="${sort == 'date_asc'}">Oldest First</option>
              <option value="title_asc" th:selected="${sort == 'title_asc'}">Title A-Z</option>
              <option value="title_desc" th:selected="${sort == 'title_desc'}">Title Z-A</option>
            </select>
          </div>
          <div class="col-md-6">
            <label for="startDate" class="form-label">From Date</label>
            <input type="date" class="form-control" id="startDate" name="startDate" th:value="${startDate}">
          </div>
          <div class="col-md-6">
            <label for="endDate" class="form-label">To Date</label>
            <input type="date" class="form-control" id="endDate" name="endDate" th:value="${endDate}">
          </div>
        </div>
        <div class="d-flex justify-content-end mt-3">
          <a th:href="@{/seller/reviews}" class="btn btn-secondary me-2">Clear Filters</a>
          <button type="submit" class="btn btn-primary">
            <i class="fas fa-filter me-2"></i>Apply Filters
          </button>
        </div>
      </form>

      <div th:if="${#maps.isEmpty(reviewedBooksWithCount)}" class="alert alert-info">
        No reviews found matching your criteria.
      </div>

      <div class="row" th:unless="${#maps.isEmpty(reviewedBooksWithCount)}">
        <div class="col-md-6 col-lg-4 mb-4" th:each="entry : ${reviewedBooksWithCount}">
          <div class="card product-review-card">
            <img th:src="${entry.key.coverImgUrl ?: '/images/book-placeholder.jpg'}" class="card-img-top product-cover" alt="Book Cover">
            <div class="card-body d-flex flex-column">
              <h5 class="card-title" th:text="${entry.key.title}">Book Title</h5>
              <p class="card-text text-muted small" th:text="${entry.key.authors}">Author</p>
              <div class="mt-auto">
                <a th:href="@{/seller/reviews/book/{bookId}(bookId=${entry.key.bookId})}" class="btn btn-primary w-100">
                  <i class="fas fa-eye me-2"></i> View <span th:text="${entry.value}"></span> Reviews
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>

      <nav th:if="${reviewPage.totalPages > 1}" aria-label="Page navigation">
        <ul class="pagination justify-content-center">
          <li class="page-item" th:classappend="${reviewPage.isFirst()} ? 'disabled'">
            <a class="page-link" th:href="@{/seller/reviews(page=${reviewPage.number - 1}, size=${reviewPage.size}, searchTitle=${searchTitle}, startDate=${startDate}, endDate=${endDate}, sort=${sort})}">Previous</a>
          </li>
          <li class="page-item" th:each="i : ${#numbers.sequence(0, reviewPage.totalPages - 1)}" th:classappend="${i == reviewPage.number} ? 'active'">
            <a class="page-link" th:href="@{/seller/reviews(page=${i}, size=${reviewPage.size}, searchTitle=${searchTitle}, startDate=${startDate}, endDate=${endDate}, sort=${sort})}" th:text="${i + 1}"></a>
          </li>
          <li class="page-item" th:classappend="${reviewPage.isLast()} ? 'disabled'">
            <a class="page-link" th:href="@{/seller/reviews(page=${reviewPage.number + 1}, size=${reviewPage.size}, searchTitle=${searchTitle}, startDate=${startDate}, endDate=${endDate}, sort=${sort})}">Next</a>
          </li>
        </ul>
      </nav>
    </main>
  </div>
</div>
<div th:replace="~{fragments/footer :: footer}"></div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>