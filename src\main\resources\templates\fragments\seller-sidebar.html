<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Seller Sidebar</title>
</head>
<body>
    <div th:fragment="seller-sidebar" class="account-sidebar">
        <div class="p-4 text-center border-bottom">
            <div th:if="${user.profilePicUrl != null && !user.profilePicUrl.isEmpty()}">
                <img th:src="${user.profilePicUrl}" alt="Profile Picture" class="profile-image mb-3">
            </div>
            <div th:unless="${user.profilePicUrl != null && !user.profilePicUrl.isEmpty()}" class="default-profile mx-auto mb-3">
                <i class="fas fa-user"></i>
            </div>
            <h5 class="mb-1" th:text="${user.fullName}">Store Name</h5>
            <p class="text-muted mb-1" th:text="${user.email}"><EMAIL></p>
            <div class="d-flex justify-content-center mt-2">
                <span th:each="role : ${roles}" class="badge bg-primary me-1" th:text="${role}">SELLER</span>
            </div>
        </div>
        <div class="list-group list-group-flush">
            <!-- Dashboard -->
            <a th:href="@{/seller/dashboard}" class="list-group-item list-group-item-action">
                <i class="fas fa-tachometer-alt"></i> Dashboard
            </a>
            
            <!-- Products -->
            <a th:href="@{/seller/products}" class="list-group-item list-group-item-action">
                <i class="fas fa-book"></i> All Products
            </a>
            <a th:href="@{/seller/products/add}" class="list-group-item list-group-item-action">
                <i class="fas fa-plus"></i> Add New Product
            </a>
            <a th:href="@{/seller/inventory}" class="list-group-item list-group-item-action">
                <i class="fas fa-boxes"></i> Inventory Management
            </a>
            
            <!-- Orders -->
            <a th:href="@{/seller/orders}" class="list-group-item list-group-item-action">
                <i class="fas fa-shopping-cart"></i> All Orders
            </a>

            <a th:href="@{/seller/orders(status=SHIPPED)}" class="list-group-item list-group-item-action">
                <i class="fas fa-truck"></i> In Shipping
            </a>
            <a th:href="@{/seller/orders(status=DELIVERED)}" class="list-group-item list-group-item-action">
                <i class="fas fa-check-circle"></i> Completed
            </a>
            <a th:href="@{/seller/orders/cancellations}" class="list-group-item list-group-item-action">
                <i class="fas fa-times-circle"></i> Cancellations
            </a>


            <!-- Customer Interaction -->

            <a th:href="@{/seller/reviews}" class="list-group-item list-group-item-action" th:classappend="${activeMenu == 'reviews' ? 'active' : ''}">
            <i class="fas fa-star"></i> Reviews
            </a>
            
            <!-- Finance -->
            <a th:href="@{/seller/analytics}" class="list-group-item list-group-item-action">
                <i class="fas fa-chart-line"></i> Analytics & Reports
            </a>
            <a th:href="@{/seller/wallet}" class="list-group-item list-group-item-action" th:classappend="${activeMenu == 'wallet' ? 'active' : ''}">
                <i class="fas fa-wallet"></i> My Wallet
            </a>

            <a th:href="@{/seller/data-management}" class="list-group-item list-group-item-action" th:classappend="${activeMenu == 'data-management' ? 'active' : ''}">
                <i class="fas fa-file-import"></i> Data Management
            </a>
            
            <!-- Store Settings -->
            <a th:href="@{/seller/shop-information}" class="list-group-item list-group-item-action" th:classappend="${activeMenu == 'shop-information' ? 'active' : ''}">
                <i class="fas fa-store"></i> Shop Information
            </a>

<!--            <a th:href="@{/seller/settings/shipping}" class="list-group-item list-group-item-action">-->
<!--                <i class="fas fa-shipping-fast"></i> Shipping Settings-->
<!--            </a>-->
<!--            <a th:href="@{/seller/settings/payment}" class="list-group-item list-group-item-action">-->
<!--                <i class="fas fa-credit-card"></i> Payment Settings-->
<!--            </a>-->
            
            <!-- Help -->

            
            <!-- Logout -->
            <form th:action="@{/seller/logout}" method="post">
                <button type="submit" class="list-group-item list-group-item-action text-danger">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </button>
            </form>
        </div>
    </div>
</body>
</html>