<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>Add New Book</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link rel="stylesheet" th:href="@{/css/admin-style.css}">

    <style>
        body { font-family: 'Open Sans', sans-serif; background-color: #F8F5F0; }
        .section-title { font-family: 'Montserrat', sans-serif; font-weight: 700; }
        .card { border: none; border-radius: 8px; box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08); }
        .btn-primary { background-color: #2C3E50; border-color: #2C3E50; }
    </style>
</head>
<body class="admin-body">

<div th:replace="~{fragments/admin-topbar :: admin-topbar}"></div>

<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-lg-3 mb-4">
            <div th:replace="~{fragments/admin-sidebar :: admin-sidebar}"></div>
        </div>

        <div class="col-lg-9">
            <main class="py-4">
                <h3 class="section-title mb-4">Add New Book</h3>

                <form th:action="@{/admin/products/add}" th:object="${book}" method="post" enctype="multipart/form-data">
                    <div class="card shadow-sm">
                        <div class="card-body p-4">
                            <div class="row g-4">
                                <div class="col-md-8">
                                    <div class="mb-3">
                                        <label for="title" class="form-label">Book Title <span class="text-danger">*</span></label>
                                        <input type="text" id="title" class="form-control" th:field="*{title}" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="authors" class="form-label">Authors</label>
                                        <input type="text" id="authors" class="form-control" th:field="*{authors}">
                                    </div>
                                    <div class="mb-3">
                                        <label for="description" class="form-label">Description</label>
                                        <textarea id="description" class="form-control" th:field="*{description}" rows="14"></textarea>
                                    </div>
                                </div>

                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="sellingPrice" class="form-label">Selling Price <span class="text-danger">*</span></label>
                                        <input type="number" id="sellingPrice" class="form-control" th:field="*{sellingPrice}" step="any" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="originalPrice" class="form-label">Original Price</label>
                                        <input type="number" id="originalPrice" class="form-control" th:field="*{originalPrice}" step="any">
                                    </div>
                                    <div class="mb-3">
                                        <label for="stockQuantity" class="form-label">Stock Quantity <span class="text-danger">*</span></label>
                                        <input type="number" id="stockQuantity" class="form-control" th:field="*{stockQuantity}" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="publisher" class="form-label">Publisher</label>
                                        <select id="publisher" class="form-select" th:field="*{publisher}">
                                            <option value="">-- Select Publisher --</option>
                                            <option th:each="pub : ${publishers}" th:value="${pub.publisherId}" th:text="${pub.publisherName}"></option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Categories</label>
                                        <div class="border rounded p-2" style="max-height: 150px; overflow-y: auto;">
                                            <div class="form-check" th:each="cat : ${categories}">
                                                <input class="form-check-input" type="checkbox" th:field="*{categories}" th:value="${cat.categoryId}" th:id="'cat_' + ${cat.categoryId}">
                                                <label class="form-check-label" th:for="'cat_' + ${cat.categoryId}" th:text="${cat.categoryName}"></label>
                                            </div>
                                        </div>
                                    </div>
                                    <hr>
                                    <div class="mb-3">
                                        <label for="coverImageFile" class="form-label">Cover Image</label>
                                        <input type="file" id="coverImageFile" name="coverImageFile" class="form-control" accept="image/*">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer bg-light text-end">
                            <a th:href="@{/admin/products}" class="btn btn-secondary">Cancel</a>
                            <button type="submit" class="btn btn-primary px-4">Add Product</button>
                        </div>
                    </div>
                </form>
            </main>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>