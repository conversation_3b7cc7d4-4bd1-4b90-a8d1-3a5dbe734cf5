<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Promotion Management - ReadHub Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Lora:wght@400;700&family=Montserrat:wght@400;700&family=Open+Sans:wght@400;600&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Open Sans', sans-serif;
            background-color: #F8F5F0;
            color: #333;
        }
        .navbar-brand {
            font-family: 'Lora', serif;
            font-weight: 700;
            font-size: 1.8rem;
            color: #2C3E50;
        }
        .section-title {
            font-family: 'Montserrat', sans-serif;
            font-weight: 700;
            margin-bottom: 1.5rem;
            color: #2C3E50;
            border-bottom: 2px solid #2C3E50;
            padding-bottom: 0.5rem;
        }
        .btn-primary {
            background-color: #2C3E50;
            border-color: #2C3E50;
        }
        .btn-primary:hover {
            background-color: #1e2b37;
            border-color: #1e2b37;
        }
        .btn-outline-primary {
            color: #2C3E50;
            border-color: #2C3E50;
        }
        .btn-outline-primary:hover {
            background-color: #2C3E50;
            border-color: #2C3E50;
        }
        .card {
            border: none;
            border-radius: 8px;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
        }
        .table th {
            font-weight: 600;
            color: #2C3E50;
        }
        .promotion-card {
            transition: transform 0.2s;
            border: none;
            border-radius: 8px;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
        }
        .promotion-card:hover {
            transform: translateY(-2px);
        }
        .status-badge {
            font-size: 0.75rem;
        }
        .usage-progress {
            height: 6px;
        }
        .profile-image {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .default-profile {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background-color: #E9ECEF;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: #6c757d;
            border: 3px solid #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
<div th:replace="~{fragments/admin-topbar :: admin-topbar}"></div>

<div class="container-fluid">
    <div class="row">
        <div class="col-lg-3 mb-4">
            <div th:replace="~{fragments/admin-sidebar :: admin-sidebar}"></div>
        </div>

        <div class="col-lg-9">
            <main class="py-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h3 class="section-title">Promotion Management</h3>
                    <div>
                        <a href="/admin/promotions/create" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Create Promotion
                        </a>
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header bg-white py-3">
                        <form method="get" th:action="@{/admin/promotions}">
                            <div class="row g-3 align-items-end">
                                <div class="col-md-4">
                                    <label for="search" class="form-label">Search</label>
                                    <input type="text" class="form-control" id="search" name="search"
                                           th:value="${search}" placeholder="Name, code, or description">
                                </div>
                                <div class="col-md-3">
                                    <label for="status" class="form-label">Status</label>
                                    <select class="form-select" id="status" name="status">
                                        <option value="">All Statuses</option>
                                        <option th:each="statusOption : ${statusOptions}"
                                                th:value="${statusOption}"
                                                th:text="${statusOption.displayName}"
                                                th:selected="${statusOption == status}">Status</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label for="isActive" class="form-label">Active</label>
                                    <select class="form-select" id="isActive" name="isActive">
                                        <option value="">All</option>
                                        <option value="true" th:selected="${isActive == true}">Active</option>
                                        <option value="false" th:selected="${isActive == false}">Inactive</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <div class="d-flex gap-2">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-search"></i> Filter
                                        </button>
                                        <a href="/admin/promotions" class="btn btn-outline-secondary">
                                            <i class="fas fa-times"></i> Clear
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-12">
                        <div class="d-flex justify-content-between align-items-center">
                                <span class="text-muted">
                                    Showing <span th:text="${promotions.numberOfElements}">0</span> of
                                    <span th:text="${totalElements}">0</span> promotions
                                </span>
                            <div class="btn-group btn-group-sm">
                                <a th:href="@{/admin/promotions(page=${currentPage}, size=10, sortBy=${sortBy}, sortDir=${sortDir}, search=${search}, status=${status}, isActive=${isActive})}"
                                   class="btn btn-outline-secondary" th:classappend="${size == 10 ? 'active' : ''}">10</a>
                                <a th:href="@{/admin/promotions(page=${currentPage}, size=25, sortBy=${sortBy}, sortDir=${sortDir}, search=${search}, status=${status}, isActive=${isActive})}"
                                   class="btn btn-outline-secondary" th:classappend="${size == 25 ? 'active' : ''}">25</a>
                                <a th:href="@{/admin/promotions(page=${currentPage}, size=50, sortBy=${sortBy}, sortDir=${sortDir}, search=${search}, status=${status}, isActive=${isActive})}"
                                   class="btn btn-outline-secondary" th:classappend="${size == 50 ? 'active' : ''}">50</a>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-12">
                        <div th:if="${promotions.empty}" class="text-center py-5">
                            <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No promotions found</h5>
                            <p class="text-muted">Create your first promotion to get started</p>
                            <a href="/admin/promotions/create" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Create Promotion
                            </a>
                        </div>

                        <div th:unless="${promotions.empty}" class="row">
                            <div class="col-lg-6 col-xl-4 mb-4" th:each="promotion : ${promotions.content}">
                                <div class="card promotion-card h-100">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="card-title mb-0" th:text="${promotion.name}">Promotion Name</h6>
                                            <small class="text-muted" th:text="${promotion.code}">CODE123</small>
                                        </div>
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle"
                                                    type="button" data-bs-toggle="dropdown">
                                                <i class="fas fa-ellipsis-v"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li>
                                                    <a class="dropdown-item" th:href="@{/admin/promotions/{id}(id=${promotion.promotionId})}">
                                                        <i class="fas fa-eye"></i> View Details
                                                    </a>
                                                </li>
                                                <li th:if="${!promotion.isActive and promotion.isNeverUsed()}">
                                                    <a class="dropdown-item" th:href="@{/admin/promotions/{id}/edit(id=${promotion.promotionId})}">
                                                        <i class="fas fa-edit"></i> Edit
                                                    </a>
                                                </li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li>
                                                    <form th:action="@{/admin/promotions/{id}/toggle-status(id=${promotion.promotionId})}"
                                                          method="post" style="display: inline;">
                                                        <button type="submit" class="dropdown-item">
                                                            <i th:classappend="${promotion.isActive ? 'fa-pause' : 'fa-play'}" class="fas"></i>
                                                            <span th:text="${promotion.isActive ? 'Deactivate' : 'Activate'}"></span>
                                                        </button>
                                                    </form>
                                                </li>
                                                <li th:if="${promotion.isNeverUsed()}">
                                                    <form th:action="@{/admin/promotions/{id}/delete(id=${promotion.promotionId})}"
                                                          method="post" style="display: inline;"
                                                          onsubmit="return confirm('Are you sure you want to delete this promotion? This action cannot be undone.')">
                                                        <button type="submit" class="dropdown-item text-danger">
                                                            <i class="fas fa-trash"></i> Delete
                                                        </button>
                                                    </form>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <div class="row mb-3">
                                            <div class="col-6">
                                                    <span class="badge status-badge"
                                                          th:classappend="${promotion.isActive ? 'bg-success' : 'bg-secondary'}"
                                                          th:text="${promotion.status.displayName}">Active</span>
                                            </div>
                                            <div class="col-6 text-end">
                                                    <span class="badge bg-info status-badge"
                                                          th:text="${promotion.scopeType.displayName}">Site-wide</span>
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <small class="text-muted">Type:</small>
                                            <div th:text="${promotion.promotionType.displayName}">Percentage Discount</div>
                                        </div>

                                        <div class="mb-3">
                                            <small class="text-muted">Discount:</small>
                                            <div>
                                                    <span th:if="${promotion.promotionType.name() == 'PERCENTAGE_DISCOUNT'}"
                                                          th:text="${promotion.discountValue} + '%'">10%</span>
                                                <span th:unless="${promotion.promotionType.name() == 'PERCENTAGE_DISCOUNT'}"
                                                      th:text="${#numbers.formatDecimal(promotion.discountValue, 1, 0)} + ' VNĐ'">10 VNĐ</span>
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <small class="text-muted">Period:</small>
                                            <div class="small">
                                                <span th:text="${#temporals.format(promotion.startDate, 'MMM dd, yyyy')}">Jan 01, 2024</span>
                                                -
                                                <span th:text="${#temporals.format(promotion.endDate, 'MMM dd, yyyy')}">Dec 31, 2024</span>
                                            </div>
                                        </div>

                                        <div th:if="${promotion.totalUsageLimit != null}">
                                            <div class="d-flex justify-content-between align-items-center mb-1">
                                                <small class="text-muted">Usage:</small>
                                                <small th:text="${promotion.currentUsageCount} + '/' + ${promotion.totalUsageLimit}">5/100</small>
                                            </div>
                                            <div class="progress usage-progress">
                                                <div class="progress-bar"
                                                     th:style="'width: ' + ${promotion.usagePercentage} + '%'"
                                                     th:classappend="${promotion.usagePercentage > 80 ? 'bg-warning' : 'bg-primary'}"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div th:if="${totalPages > 1}" class="row">
                    <div class="col-12">
                        <nav aria-label="Promotion pagination">
                            <ul class="pagination justify-content-center">
                                <li class="page-item" th:classappend="${currentPage == 0 ? 'disabled' : ''}">
                                    <a class="page-link"
                                       th:href="@{/admin/promotions(page=${currentPage - 1}, size=${size}, sortBy=${sortBy}, sortDir=${sortDir}, search=${search}, status=${status}, isActive=${isActive})}">
                                        Previous
                                    </a>
                                </li>

                                <li th:each="pageNum : ${#numbers.sequence(0, totalPages - 1)}"
                                    class="page-item"
                                    th:classappend="${pageNum == currentPage ? 'active' : ''}">
                                    <a class="page-link"
                                       th:href="@{/admin/promotions(page=${pageNum}, size=${size}, sortBy=${sortBy}, sortDir=${sortDir}, search=${search}, status=${status}, isActive=${isActive})}"
                                       th:text="${pageNum + 1}">1</a>
                                </li>

                                <li class="page-item" th:classappend="${currentPage == totalPages - 1 ? 'disabled' : ''}">
                                    <a class="page-link"
                                       th:href="@{/admin/promotions(page=${currentPage + 1}, size=${size}, sortBy=${sortBy}, sortDir=${sortDir}, search=${search}, status=${status}, isActive=${isActive})}">
                                        Next
                                    </a>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>

<div th:if="${success}" class="toast-container position-fixed bottom-0 end-0 p-3">
    <div class="toast show" role="alert">
        <div class="toast-header bg-success text-white">
            <i class="fas fa-check-circle me-2"></i>
            <strong class="me-auto">Success</strong>
            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast"></button>
        </div>
        <div class="toast-body" th:text="${success}">Success message</div>
    </div>
</div>

<div th:if="${error}" class="toast-container position-fixed bottom-0 end-0 p-3">
    <div class="toast show" role="alert">
        <div class="toast-header bg-danger text-white">
            <i class="fas fa-exclamation-circle me-2"></i>
            <strong class="me-auto">Error</strong>
            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast"></button>
        </div>
        <div class="toast-body" th:text="${error}">Error message</div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>