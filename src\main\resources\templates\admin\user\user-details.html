<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Details - ReadHub</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Lora:wght@400;700&family=Montserrat:wght@400;700&family=Open+Sans:wght@400;600&display=swap" rel="stylesheet">

    <link rel="stylesheet" th:href="@{/css/admin-layout.css}">

</head>
<body>

<div th:replace="~{fragments/admin-topbar :: admin-topbar}"></div>

<div class="container my-4">
    <div class="row">
        <div class="col-lg-3 mb-4">
            <div th:replace="~{fragments/admin-sidebar :: admin-sidebar}"></div>
        </div>

        <div class="col-lg-9">
            <main th:if="${user != null}">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2 class="section-title mb-0">User Profile</h2>
                    <div>
                        <a th:href="@{/admin/users}" class="btn btn-secondary"><i class="fas fa-arrow-left me-2"></i>Back to List</a>
                        <a th:href="@{'/admin/users/edit/' + ${user.userId}}" class="btn btn-primary"><i class="fas fa-edit me-2"></i>Edit User</a>
                    </div>
                </div>

                <div class="row">
                    <div class="col-lg-4">
                        <div class="card shadow-sm mb-4">
                            <div class="card-body text-center">
                                <img th:src="${user.profilePicUrl != null ? user.profilePicUrl : '/images/default-avatar.png'}"
                                     alt="User Avatar" class="profile-image mb-3">
                                <h4 class="mb-1" th:text="${user.fullName}"></h4>
                                <p class="text-muted mb-2" th:text="${user.email}"></p>
                                <div>
                                    <span th:each="userRole : ${user.userRoles}"
                                          class="badge rounded-pill me-1" th:text="${userRole.role.roleName}"
                                          th:classappend="${userRole.role.roleName == 'ADMIN' ? 'bg-danger' : 'bg-primary'}"></span>
                                </div>
                            </div>
                            <ul class="list-group list-group-flush p-3">
                                <li class="list-group-item">
                                    <strong>Phone</strong>
                                    <span class="text-secondary" th:text="${user.phone ?: 'N/A'}"></span>
                                </li>
                                <li class="list-group-item">
                                    <strong>Date of Birth</strong>
                                    <span class="text-secondary" th:text="${user.dateOfBirth != null ? #temporals.format(user.dateOfBirth, 'dd / MM / yyyy') : 'N/A'}"></span>
                                </li>
                                <li class="list-group-item">
                                    <strong>Gender</strong>
                                    <span class="text-secondary" th:switch="${user.gender}">
                                        <span th:case="0">Male</span>
                                        <span th:case="1">Female</span>
                                        <span th:case="*">Other</span>
                                    </span>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <div class="col-lg-8">
                        <div class="card shadow-sm">
                            <div class="card-body">
                                <ul class="nav nav-tabs" id="detailsTab" role="tablist">
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link active" id="addresses-tab" data-bs-toggle="tab" data-bs-target="#addresses-tab-pane" type="button">Addresses</button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="orders-tab" data-bs-toggle="tab" data-bs-target="#orders-tab-pane" type="button">Order History</button>
                                    </li>
                                </ul>
                                <div class="tab-content pt-3" id="detailsTabContent">
                                    <div class="tab-pane fade show active" id="addresses-tab-pane" role="tabpanel">
                                        <h5>Saved Addresses (<span th:text="${user.addresses != null ? #lists.size(user.addresses) : 0}"></span>)</h5>
                                        <hr>
                                        <div th:if="${user.addresses == null or #lists.isEmpty(user.addresses)}" class="alert alert-light">
                                            This user has not saved any addresses.
                                        </div>
                                        <div th:each="address, iterStat : ${user.addresses}" class="p-3 mb-2 border rounded bg-light">
                                            <h6>Address <span th:text="${iterStat.count}"></span></h6>
                                            <p class="mb-1" th:text="${address.addressDetail}"></p>
                                            <p class="mb-0 text-muted" th:text="${address.ward + ', ' + address.district + ', ' + address.province}"></p>
                                        </div>
                                    </div>
                                    <div class="tab-pane fade" id="orders-tab-pane" role="tabpanel">
                                        <h5>Order History (<span th:text="${user.orders != null ? #lists.size(user.orders) : 0}"></span>)</h5>
                                        <hr>
                                        <div th:if="${user.orders == null or #lists.isEmpty(user.orders)}" class="alert alert-light">
                                            This user has no orders.
                                        </div>
                                        <div th:if="${user.orders != null and !#lists.isEmpty(user.orders)}" class="table-responsive">
                                            <table class="table table-sm table-striped">
                                                <thead><tr><th>ID</th><th>Date</th><th>Total</th><th>Status</th></tr></thead>
                                                <tbody>
                                                <tr th:each="order : ${user.orders}">
                                                    <td th:text="${order.orderId}"></td>
                                                    <td th:text="${#temporals.format(order.orderDate, 'dd-MM-yyyy')}"></td>
                                                    <td th:text="${#numbers.formatDecimal(order.totalAmount, 0, 'COMMA', 0, 'POINT')} + ' ₫'"></td>
                                                    <td><span class="badge bg-info" th:text="${order.orderStatus}"></span></td>
                                                </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>