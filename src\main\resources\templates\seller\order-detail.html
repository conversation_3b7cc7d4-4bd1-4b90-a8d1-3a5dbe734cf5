<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title th:text="'Order Details - #' + ${order.orderId}"></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Lora:wght@400;700&family=Montserrat:wght@400;700&family=Open+Sans:wght@400;600&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Open Sans', sans-serif; background-color: #F8F5F0; color: #333; }
        .section-title { font-family: 'Montserrat', sans-serif; font-weight: 700; margin-bottom: 1.5rem; color: #2C3E50; border-bottom: 2px solid #2C3E50; padding-bottom: 0.5rem; }
        .account-container { background-color: #fff; border-radius: 8px; box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08); padding: 2rem; margin-bottom: 2rem; }
        .detail-row { display: flex; justify-content: space-between; padding: 0.5rem 0; border-bottom: 1px solid #eee; }
        .detail-row .label { color: #6c757d; }
        .detail-row .value { font-weight: 600; }
    </style>
</head>
<body>

<div th:replace="~{fragments/seller-topbar :: seller-topbar}"></div>
<div class="container my-5">
    <div class="row">
        <div class="col-lg-3 mb-4">
            <div th:replace="~{fragments/seller-sidebar :: seller-sidebar}"></div>
        </div>

        <div class="col-lg-9">
            <main th:object="${order}">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h3 class="section-title" th:text="'Order Details #' + *{orderId}"></h3>
                    <a th:href="@{/seller/orders}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Orders
                    </a>
                </div>

                <div class="row">
                    <div class="col-md-7">
                        <div class="account-container">
                            <h5><i class="fas fa-shipping-fast me-2"></i>Shipping Information</h5>
                            <hr>
                            <div class="detail-row" th:if="*{customerOrder}"><span class="label">Recipient Name:</span> <span class="value" th:text="*{customerOrder.recipientName}"></span></div>
                            <div class="detail-row" th:if="*{customerOrder}"><span class="label">Phone:</span> <span class="value" th:text="*{customerOrder.recipientPhone}"></span></div>
                            <div class="detail-row" th:if="*{customerOrder}">
                                <span class="label">Address:</span>
                                <span class="value text-end">
                                    <span th:text="*{customerOrder.shippingAddressDetail}"></span>,
                                    <span th:text="*{customerOrder.shippingWard}"></span>,
                                    <span th:text="*{customerOrder.shippingDistrict}"></span>,
                                    <span th:text="*{customerOrder.shippingProvince}"></span>
                                </span>
                            </div>
                            <div class="detail-row" th:if="*{customerOrder}"><span class="label">Address Type:</span> <span class="value" th:text="*{customerOrder.shippingAddressType == 0 ? 'Home' : 'Company'}"></span></div>
                            <div class="detail-row" th:if="*{customerOrder != null and customerOrder.shippingCompany != null}"><span class="label">Shipping Company:</span> <span class="value" th:text="*{customerOrder.shippingCompany}"></span></div>
                            <div class="detail-row" th:if="*{notes}"><span class="label">Notes:</span> <span class="value" th:text="*{notes}"></span></div>
                        </div>

                        <div class="account-container">
                            <h5><i class="fas fa-credit-card me-2"></i>Payment Details</h5>
                            <hr>
                            <div class="detail-row" th:if="*{customerOrder}"><span class="label">Payment Method:</span> <span class="value" th:text="*{customerOrder.paymentMethod}"></span></div>
                            <div class="detail-row" th:if="*{customerOrder}"><span class="label">Payment Status:</span> <span class="value" th:text="*{customerOrder.paymentStatus}"></span></div>
                        </div>
                    </div>

                    <div class="col-md-5">
                        <div class="account-container">
                            <h5><i class="fas fa-receipt me-2"></i>Order Summary</h5>
                            <hr>
                            <div class="detail-row"><span class="label">Order Date:</span> <span class="value" th:text="*{#temporals.format(orderDate, 'dd MMM yyyy, HH:mm')}"></span></div>
                            <div class="detail-row"><span class="label">Order Status:</span> <span class="value" th:text="*{orderStatus}"></span></div>
                            <div class="detail-row" th:if="*{updatedAt}"><span class="label">Last Updated:</span> <span class="value" th:text="*{#temporals.format(updatedAt, 'dd MMM yyyy, HH:mm')}"></span></div>
                            <div class="detail-row" th:if="*{cancelledAt}"><span class="label">Cancelled At:</span> <span class="value" th:text="*{#temporals.format(cancelledAt, 'dd MMM yyyy, HH:mm')}"></span></div>
                            <div class="detail-row" th:if="*{cancellationReason}"><span class="label">Reason:</span> <span class="value" th:text="*{cancellationReason}"></span></div>
                        </div>

                        <div class="account-container">
                            <h5><i class="fas fa-dollar-sign me-2"></i>Financials</h5>
                            <hr>
                            <div class="detail-row">
                                <span class="label">Sub Total:</span>
                                <span class="value" th:text="*{#numbers.formatDecimal(subTotal, 0, 'COMMA', 0, 'POINT') + ' VND'}"></span>
                            </div>
                            <div class="detail-row">
                                <span class="label">Shipping Fee:</span>
                                <span class="value" th:text="*{#numbers.formatDecimal(shippingFee, 0, 'COMMA', 0, 'POINT') + ' VND'}"></span>
                            </div>
                            <div class="detail-row">
                                <span class="label">Discount:</span>
                                <span class="value text-danger" th:text="*{#numbers.formatDecimal(discountAmount, 0, 'COMMA', 0, 'POINT') + ' VND'}"></span>
                            </div>
                            <hr class="my-2">
                            <div class="detail-row fs-5">
                                <span class="label">Total Amount:</span>
                                <span class="value text-primary" th:text="*{#numbers.formatDecimal(totalAmount, 0, 'COMMA', 0, 'POINT') + ' VND'}"></span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="account-container">
                    <h5 class="section-title">Items in this Order</h5>
                    <table class="table">
                        <thead>
                        <tr>
                            <th>Product</th>
                            <th>Price</th>
                            <th>Quantity</th>
                            <th class="text-end">Subtotal</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr th:each="item : *{orderItems}">
                            <td th:text="${item.book.title}"></td>

                            <td th:text="${#numbers.formatDecimal(item.unitPrice, 0, 'COMMA', 0, 'POINT') + ' VND'}"></td>

                            <td th:text="${item.quantity}"></td>

                            <td class="text-end" th:text="${#numbers.formatDecimal(item.subtotal, 0, 'COMMA', 0, 'POINT') + ' VND'}"></td>
                        </tr>
                        </tbody>
                    </table>
                </div>

            </main>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
            </main>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>