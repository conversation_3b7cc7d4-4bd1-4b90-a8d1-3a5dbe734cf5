<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>Edit User</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link rel="stylesheet" th:href="@{/css/admin-style.css}">
    <<link rel="stylesheet" th:href="@{/css/admin-layout.css}">

</head>
<body>

<div th:replace="~{fragments/admin-topbar :: admin-topbar}"></div>

<div class="container my-4">
    <div class="row">
        <div class="col-lg-3 mb-4">
            <div th:replace="~{fragments/admin-sidebar :: admin-sidebar}"></div>
        </div>

        <div class="col-lg-9">
            <main class="py-4" th:if="${user != null}">
                <h3 class="section-title">Edit User Account</h3>
                <div th:if="${warningMessage}" class="alert alert-warning" role="alert" th:text="${warningMessage}"></div>

                <div class="card shadow-sm">
                    <div class="card-body p-4">
                        <form th:action="@{'/admin/users/update/' + ${user.userId}}" method="post">
                            <fieldset disabled>
                                <div class="mb-3"><label class="form-label">Full Name</label><input type="text" class="form-control" th:value="*{user.fullName}"></div>
                                <div class="mb-3"><label class="form-label">Email</label><input type="email" class="form-control" th:value="*{user.email}"></div>
                            </fieldset>
                            <hr>

                            <div class="mb-4">
                                <label class="form-label fw-bold">Roles</label>
                                <small th:if="${user.userId == loggedInAdminId}" class="form-text text-muted d-block mb-2">
                                    You cannot modify your own roles.
                                </small>
                                <div th:each="role : ${allRoles}" class="form-check">
                                    <input class="form-check-input" type="checkbox" name="roles"
                                           th:value="${role.roleName}"
                                           th:id="'role_' + ${role.roleId}"
                                           th:checked="${currentUserRoles.contains(role.roleName)}"
                                           th:disabled="${user.userId == loggedInAdminId}"> <label class="form-check-label" th:for="'role_' + ${role.roleId}" th:text="${role.roleName}"></label>
                                </div>
                            </div>
                            <hr>

                            <div class="mb-4">
                                <label class="form-label fw-bold">Account Status</label>
                                <small th:if="${user.userId == loggedInAdminId}" class="form-text text-muted d-block mb-2">
                                    You cannot block your own account.
                                </small>
                                <div class="form-check form-switch fs-5">
                                    <input class="form-check-input" type="checkbox" role="switch" id="statusSwitch" name="isActive" value="true"
                                           th:checked="${user.isActive}"
                                           th:disabled="${user.userId == loggedInAdminId}"> <label class="form-check-label" for="statusSwitch" th:text="${user.isActive ? 'Active' : 'Blocked'}"></label>
                                </div>
                            </div>

                            <a th:href="@{/admin/users}" class="btn btn-secondary">Cancel</a>
                            <button type="submit" class="btn btn-primary" th:disabled="${user.userId == loggedInAdminId}">Save Changes</button>
                        </form>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>