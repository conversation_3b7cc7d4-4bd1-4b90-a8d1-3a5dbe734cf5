<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Wallet - ReadHub Seller</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Lora:wght@400;700&family=Montserrat:wght@400;700&family=Open+Sans:wght@400;600&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" th:href="@{/css/seller-style.css}">

    <style>
        .wallet-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .wallet-balance {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .wallet-actions .btn {
            margin: 0.25rem;
        }

        .transaction-item {
            border-left: 4px solid #dee2e6;
            padding-left: 1rem;
            margin-bottom: 1rem;
        }

        .transaction-item.credit {
            border-left-color: #28a745;
        }

        .transaction-item.debit {
            border-left-color: #dc3545;
        }

        .transaction-amount.credit {
            color: #28a745;
        }

        .transaction-amount.debit {
            color: #dc3545;
        }
    </style>
</head>
<body>

<div class="topbar-wrapper" th:replace="fragments/seller-topbar :: seller-topbar"></div>

<div class="container-fluid">
    <div class="row">
        <div class="col-lg-3 mb-4 sidebar-wrapper">
            <div th:replace="fragments/seller-sidebar :: seller-sidebar"></div>
        </div>

        <div class="col-lg-9 main-content-wrapper">
            <main class="py-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h3 class="section-title mb-0">
                        <i class="fas fa-wallet text-primary me-2"></i>My Wallet
                    </h3>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item"><a th:href="@{/seller/dashboard}">Dashboard</a></li>
                            <li class="breadcrumb-item active">My Wallet</li>
                        </ol>
                    </nav>
                </div>

                <!-- Error Message -->
                <div th:if="${errorMessage}" class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <span th:text="${errorMessage}"></span>
                </div>

                <!-- Success Message -->
                <div th:if="${successMessage}" class="alert alert-success" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    <span th:text="${successMessage}"></span>
                </div>

                <!-- Wallet Balance Card -->
                <div class="wallet-card">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h3 class="mb-2">
                                <i class="fas fa-wallet me-2"></i>
                                Wallet Balance
                            </h3>
                            <div class="wallet-balance" th:text="${walletBalance.formattedBalance}">0 VND</div>
                            <p class="mb-0 opacity-75">Available for purchases and refunds</p>
                        </div>
                        <div class="col-md-4 text-md-end">
                            <div class="wallet-actions">
                                <a th:href="@{/seller/wallet/withdraw}" class="btn btn-warning btn-sm me-2">
                                    <i class="fas fa-money-bill-wave me-1"></i> Withdraw
                                </a>
                                <a th:href="@{/seller/wallet/transactions}" class="btn btn-light btn-sm">
                                    <i class="fas fa-history me-1"></i> View History
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Transactions -->
                <div class="card">
                    <div class="card-header bg-white py-3">
                        <div class="row align-items-center">
                            <div class="col">
                                <h5 class="mb-0">Recent Transactions</h5>
                            </div>
                            <div class="col-auto">
                                <a th:href="@{/seller/wallet/transactions}" class="btn btn-outline-primary btn-sm">
                                    View All
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div th:if="${recentTransactions.empty}" class="text-center py-4 text-muted">
                            <i class="fas fa-receipt fa-3x mb-3"></i>
                            <p class="mb-0">No transactions yet</p>
                            <small>Your wallet transactions will appear here</small>
                        </div>

                        <div th:unless="${recentTransactions.empty}">
                            <div th:each="transaction : ${recentTransactions.content}"
                                 class="transaction-item"
                                 th:classappend="${transaction.transactionType.name().toLowerCase()}">
                                <div class="row align-items-center">
                                    <div class="col">
                                        <div class="fw-bold" th:text="${transaction.description}">Transaction Description</div>
                                        <small class="text-muted">
                                            <span th:text="${#temporals.format(transaction.createdAt, 'dd/MM/yyyy HH:mm')}">Date</span>
                                            <span th:if="${transaction.referenceType}" class="ms-2">
                                                • <span th:text="${transaction.referenceType.name()}">Reference Type</span>
                                            </span>
                                        </small>
                                    </div>
                                    <div class="col-auto">
                                        <div class="transaction-amount fw-bold"
                                             th:classappend="${transaction.transactionType.name().toLowerCase()}">
                                            <span th:if="${transaction.transactionType.name() == 'CREDIT'}">+</span>
                                            <span th:if="${transaction.transactionType.name() == 'DEBIT'}">-</span>
                                            <span th:text="${#numbers.formatDecimal(transaction.amount, 0, 'COMMA', 0, 'POINT')} + ' VND'">Amount</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </main>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
