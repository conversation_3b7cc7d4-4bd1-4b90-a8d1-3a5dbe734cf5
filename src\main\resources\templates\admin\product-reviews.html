<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
  <meta charset="UTF-8">
  <title>Product Review Moderation</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
  <link rel="stylesheet" th:href="@{/css/admin-style.css}">
  <style>
    .sort-link { text-decoration: none; color: inherit; }
    .sort-link:hover { color: #0d6efd; }
  </style>
</head>
<body>
<div th:replace="~{fragments/admin-topbar :: admin-topbar}"></div>
<div class="container-fluid">
  <div class="row">
    <div class="col-lg-3 mb-4">
      <div th:replace="~{fragments/admin-sidebar :: admin-sidebar}"></div>
    </div>
    <div class="col-lg-9">
      <main class="py-4">
        <h3 class="section-title">Product Review Moderation</h3>

        <div th:if="${successMessage}" class="alert alert-success alert-dismissible fade show" role="alert">
          <span th:text="${successMessage}"></span>
          <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <div th:if="${errorMessage}" class="alert alert-danger alert-dismissible fade show" role="alert">
          <span th:text="${errorMessage}"></span>
          <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>

        <div class="card">
          <div class="card-header bg-white py-3">
            <form th:action="@{/admin/product-reviews}" method="get">
              <div class="row g-2 align-items-center">
                <div class="col-md-4"><input type="text" name="search" class="form-control form-control-sm" placeholder="Search by content..." th:value="${search}"></div>
                <div class="col-md-2">
                  <select name="rating" class="form-select form-select-sm">
                    <option value="">All Ratings</option>
                    <option th:each="r : ${#numbers.sequence(5,1)}" th:value="${r}" th:text="${r} + ' Stars'" th:selected="${rating == r}"></option>
                  </select>
                </div>
                <div class="col-md-2"><input type="date" name="startDate" class="form-control form-control-sm" th:value="${startDate}"></div>
                <div class="col-md-2"><input type="date" name="endDate" class="form-control form-control-sm" th:value="${endDate}"></div>
                <div class="col-md-2 d-flex gap-2">
                  <button type="submit" class="btn btn-sm btn-primary w-100">Filter</button>
                  <a th:href="@{/admin/product-reviews}" class="btn btn-sm btn-secondary" title="Clear"><i class="fas fa-times"></i></a>
                </div>
              </div>
            </form>
          </div>
          <div class="card-body">
            <div class="table-responsive">
              <table class="table table-hover align-middle">
                <thead>
                <tr>
                  <th>User</th>
                  <th>Rating</th>
                  <th>Book Title</th>
                  <th>Review Content</th>
                  <th>Date</th>
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
                </thead>
                <tbody>
                <tr th:each="review : ${reviews.content}">
                  <td th:text="${review.user.fullName}"></td>
                  <td><span th:each="i : ${#numbers.sequence(1, review.rating)}" class="text-warning"><i class="fas fa-star"></i></span></td>
                  <td th:text="${review.orderItem?.book?.title} ? ${#strings.abbreviate(review.orderItem.book.title, 50)} : 'N/A'"></td>
                  <td th:text="${#strings.abbreviate(review.content, 80)}"></td>
                  <td th:text="${#temporals.format(review.createdDate, 'dd-MM-yyyy HH:mm')}"></td>
                  <td><span class="badge" th:classappend="${review.isApproved} ? 'bg-success' : 'bg-warning'" th:text="${review.isApproved} ? 'Approved' : 'Pending'"></span></td>
                  <td>
                    <a th:href="@{/admin/moderation/reviews/{id}(id=${review.reviewId})}" class="btn btn-sm btn-info" title="View Details"><i class="fas fa-eye"></i></a>
                    <form th:if="${!review.isApproved}" th:action="@{/admin/reviews/approve/{id}(id=${review.reviewId})}" method="post" class="d-inline">
                      <button type="submit" class="btn btn-sm btn-success" title="Approve"><i class="fas fa-check"></i></button>
                    </form>
                    <form th:action="@{/admin/reviews/delete/{id}(id=${review.reviewId})}" method="post" onsubmit="return confirm('Delete this review?')" class="d-inline">
                      <button type="submit" class="btn btn-sm btn-danger" title="Delete"><i class="fas fa-trash"></i></button>
                    </form>
                  </td>
                </tr>
                <tr th:if="${reviews.empty}"><td colspan="7" class="text-center p-3">No reviews found.</td></tr>
                </tbody>
              </table>
            </div>
          </div>
          <div class="card-footer bg-white py-3" th:if="${reviews.totalPages > 1}">
            <nav>
              <ul class="pagination pagination-sm justify-content-center mb-0">
                <li class="page-item" th:classappend="${reviews.first ? 'disabled' : ''}">
                  <a class="page-link" th:href="@{/admin/product-reviews(page=0, search=${search}, rating=${rating}, sortField=${sortField}, sortDir=${sortDir})}">First</a>
                </li>
                <li class="page-item" th:classappend="${!reviews.hasPrevious() ? 'disabled' : ''}">
                  <a class="page-link" th:href="@{/admin/product-reviews(page=${reviews.number - 1}, search=${search}, rating=${rating}, sortField=${sortField}, sortDir=${sortDir})}">Previous</a>
                </li>
                <li class="page-item" th:each="i : ${#numbers.sequence(0, reviews.totalPages - 1)}" th:classappend="${i == reviews.number ? 'active' : ''}">
                  <a class="page-link" th:href="@{/admin/product-reviews(page=${i}, search=${search}, rating=${rating}, sortField=${sortField}, sortDir=${sortDir})}" th:text="${i + 1}"></a>
                </li>
                <li class="page-item" th:classappend="${!reviews.hasNext() ? 'disabled' : ''}">
                  <a class="page-link" th:href="@{/admin/product-reviews(page=${reviews.number + 1}, search=${search}, rating=${rating}, sortField=${sortField}, sortDir=${sortDir})}">Next</a>
                </li>
                <li class="page-item" th:classappend="${reviews.last ? 'disabled' : ''}">
                  <a class="page-link" th:href="@{/admin/product-reviews(page=${reviews.totalPages - 1}, search=${search}, rating=${rating}, sortField=${sortField}, sortDir=${sortDir})}">Last</a>
                </li>
              </ul>
            </nav>
          </div>
        </div>
      </main>
    </div>
  </div>
</div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>