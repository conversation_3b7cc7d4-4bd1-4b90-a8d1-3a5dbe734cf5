<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Product Management - ReadHub Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Lora:wght@400;700&family=Montserrat:wght@400;700&family=Open+Sans:wght@400;600&display=swap" rel="stylesheet">

    <style>
        body { font-family: 'Open Sans', sans-serif; background-color: #F8F5F0; color: #333; }
        .navbar-brand { font-family: 'Lora', serif; font-weight: 700; font-size: 1.8rem; color: #2C3E50; }
        .section-title { font-family: 'Montserrat', sans-serif; font-weight: 700; margin-bottom: 1.5rem; color: #2C3E50; border-bottom: 2px solid #2C3E50; padding-bottom: 0.5rem; }
        .btn-primary { background-color: #2C3E50; border-color: #2C3E50; }
        .btn-primary:hover { background-color: #1e2b37; border-color: #1e2b37; }
        .btn-outline-primary { color: #2C3E50; border-color: #2C3E50; }
        .btn-outline-primary:hover { background-color: #2C3E50; border-color: #2C3E50; }
        .card { border: none; border-radius: 8px; box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08); }
        .table th { font-weight: 600; color: #2C3E50; }
        .object-fit-cover { object-fit: cover; }
        .profile-image { width: 120px; height: 120px; border-radius: 50%; object-fit: cover; border: 3px solid #fff; box-shadow: 0 4px 8px rgba(0,0,0,0.1); }
        .default-profile { width: 120px; height: 120px; border-radius: 50%; background-color: #E9ECEF; display: flex; align-items: center; justify-content: center; font-size: 3rem; color: #6c757d; border: 3px solid #fff; box-shadow: 0 4px 8px rgba(0,0,0,0.1); }
    </style>
</head>
<body>
<div th:replace="~{fragments/admin-topbar :: admin-topbar}"></div>

<div class="container-fluid">
    <div class="row">
        <div class="col-lg-3 mb-4">
            <div th:replace="~{fragments/admin-sidebar :: admin-sidebar}"></div>
        </div>

        <div class="col-lg-9">
            <main class="py-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h3 class="section-title">Book Management</h3>

                </div>

                <div class="card">
                    <div class="card-header bg-white py-3">
                        <form th:action="@{/admin/products}" method="get" id="filterForm">
                            <input type="hidden" name="sortField" th:value="${sortField}">
                            <input type="hidden" name="sortDir" th:value="${sortDir}">

                            <div class="row gx-2 gy-2 align-items-center">
                                <div class="col-md-5">
                                    <div class="input-group">
                                        <input class="form-control" type="search" name="keyword" placeholder="Search by title..." th:value="${keyword}">
                                        <button class="btn btn-outline-primary" type="submit" title="Search">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                </div>

                                <div class="col-md-3">
                                    <select class="form-select" name="categoryId" onchange="this.form.submit()">
                                        <option value="">All Categories</option>
                                        <option th:each="cat : ${allCategories}"
                                                th:value="${cat.categoryId}"
                                                th:text="${cat.categoryName}"
                                                th:selected="${selectedCategoryId != null && cat.categoryId == selectedCategoryId}">
                                        </option>
                                    </select>
                                </div>
                                <div class="col-md-auto">
                                    <div class="dropdown">
                                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                            Sort by
                                        </button>
                                        <ul class="dropdown-menu dropdown-menu-end">
                                            <li><a class="dropdown-item" th:href="@{/admin/products(sortField='dateAdded', sortDir='DESC', keyword=${keyword}, categoryId=${selectedCategoryId})}">Newest first</a></li>
                                            <li><a class="dropdown-item" th:href="@{/admin/products(sortField='dateAdded', sortDir='ASC', keyword=${keyword}, categoryId=${selectedCategoryId})}">Oldest first</a></li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li><a class="dropdown-item" th:href="@{/admin/products(sortField='title', sortDir='ASC', keyword=${keyword}, categoryId=${selectedCategoryId})}">Title (A-Z)</a></li>
                                            <li><a class="dropdown-item" th:href="@{/admin/products(sortField='title', sortDir='DESC', keyword=${keyword}, categoryId=${selectedCategoryId})}">Title (Z-A)</a></li>
                                        </ul>
                                    </div>
                                </div>

                                <div class="col-md-auto">
                                    <a th:href="@{/admin/products}" class="btn btn-outline-secondary" title="Clear search and sort">
                                        <i class="fas fa-times"></i> Clear
                                    </a>
                                </div>
                            </div>
                        </form>
                    </div>

                </div>


                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover align-middle">
                                <thead>
                                <tr>
                                    <th width="80">Image</th>
                                    <th>Title</th>
                                    <th>Price</th>
                                    <th>Categories</th>
                                    <th>Status</th>
                                    <th width="120">Actions</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr th:if="${bookPage.empty}">
                                    <td colspan="6" class="text-center p-4">No books found.</td>
                                </tr>
                                <tr th:each="book : ${bookPage.content}">
                                    <td>
                                        <img th:src="${book.coverImgUrl != null ? book.coverImgUrl : '/images/default-book.png'}" alt="Book Cover"
                                             width="40" height="60" class="rounded object-fit-cover">
                                    </td>
                                    <td th:text="${book.title}"></td>
                                    <td th:text="${#numbers.formatDecimal(book.sellingPrice, 0, 'COMMA', 0, 'POINT')} + ' ₫'"></td>

                                    <td>
                                        <span th:each="cat, iter : ${book.categories}" th:text="${cat.categoryName} + (${!iter.last} ? ', ' : '')"></span>
                                    </td>

                                    <td>
                                        <div th:switch="${book.active}">
                                            <span th:case="false" class="badge bg-secondary">Hidden</span>
                                            <div th:case="*">
            <span class="badge"
                  th:classappend="${book.stockQuantity > 0 ? 'bg-success' : 'bg-danger'}"
                  th:text="${book.stockQuantity > 0 ? 'In Stock' : 'Out of Stock'}"></span>
                                            </div>
                                        </div>
                                    </td>

                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a th:href="@{'/admin/products/detail/' + ${book.bookId}}" class="btn btn-outline-secondary" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a th:href="@{'/admin/products/edit/' + ${book.bookId}}" class="btn btn-outline-primary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <form th:action="@{'/admin/products/delete/' + ${book.bookId}}" method="post" class="d-inline"
                                                  onsubmit="return confirm('Are you sure you want to hide this product? It will not be visible to customers.');">
                                                <button type="submit" class="btn btn-sm btn-outline-danger" title="Hide Product">
                                                    <i class="fas fa-trash-alt"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div class="card-footer bg-white py-3" th:if="${bookPage.totalPages > 1}">
                        <div class="row align-items-center">
                            <div class="col-md-6 text-muted small">
                                Showing <strong th:text="${bookPage.number * bookPage.size + 1}"></strong>
                                to <strong th:text="${bookPage.number * bookPage.size + bookPage.numberOfElements}"></strong>
                                of <strong th:text="${bookPage.totalElements}"></strong> entries
                            </div>
                            <div class="col-md-6">
                                <nav aria-label="Product table navigation" class="float-md-end">
                                    <ul class="pagination pagination-sm mb-0">
                                        <li class="page-item" th:classappend="${bookPage.isFirst() ? 'disabled' : ''}">
                                            <a class="page-link" th:href="@{/admin/products(page=${bookPage.number - 1}, keyword=${keyword}, sortField=${sortField}, sortDir=${sortDir})}">Previous</a>
                                        </li>
                                        <li class="page-item" th:each="i : ${#numbers.sequence(0, bookPage.totalPages - 1)}" th:classappend="${i == bookPage.number ? 'active' : ''}">
                                            <a class="page-link" th:href="@{/admin/products(page=${i}, keyword=${keyword}, sortField=${sortField}, sortDir=${sortDir})}" th:text="${i + 1}"></a>
                                        </li>
                                        <li class="page-item" th:classappend="${bookPage.isLast() ? 'disabled' : ''}">
                                            <a class="page-link" th:href="@{/admin/products(page=${bookPage.number + 1}, keyword=${keyword}, sortField=${sortField}, sortDir=${sortDir})}">Next</a>
                                        </li>
                                    </ul>
                                </nav>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>