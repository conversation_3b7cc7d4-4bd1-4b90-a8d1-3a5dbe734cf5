<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Consolidated Reports - ReadHub Admin</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Lora:wght@400;700&family=Montserrat:wght@400;700&family=Open+Sans:wght@400;600&display=swap" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Custom CSS -->
    <style>
        body {
            font-family: 'Open Sans', sans-serif;
            background-color: #F8F5F0;
            color: #333;
        }
        .navbar-brand {
            font-family: 'Lora', serif;
            font-weight: 700;
            font-size: 1.8rem;
            color: #2C3E50;
        }
        .section-title {
            font-family: 'Montserrat', sans-serif;
            font-weight: 700;
            margin-bottom: 1.5rem;
            color: #2C3E50;
            border-bottom: 2px solid #2C3E50;
            padding-bottom: 0.5rem;
        }
        .account-container {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 10px;
            margin-bottom: 1rem;
            position: relative;
            overflow: hidden;
            transition: transform 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-5px);
        }
        .stat-card h4 {
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
            opacity: 0.9;
        }
        .stat-card p {
            font-size: 1.8rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        .stat-card .trend {
            font-size: 0.8rem;
            opacity: 0.8;
        }
        .stat-card i {
            position: absolute;
            right: 1rem;
            top: 1rem;
            font-size: 2rem;
            opacity: 0.3;
        }
        .stat-card.orders {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        }
        .stat-card.users {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .stat-card.products {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        .stat-card.forum {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        .stat-card.revenue {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            color: #333;
        }
        .chart-container {
            position: relative;
            height: 300px;
            margin-bottom: 2rem;
        }
        .chart-container.large {
            height: 400px;
        }
        .overview-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }
        .overview-header h3 {
            margin-bottom: 0;
        }
        .period-badge {
            background-color: #ecf0f1;
            color: #2C3E50;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
        }
        .filter-card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.05);
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        .btn-filter {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 0.5rem 1.5rem;
            border-radius: 25px;
            transition: all 0.3s ease;
        }
        .btn-filter:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            color: white;
        }
        .metric-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        .chart-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }
        .table-responsive {
            max-height: 400px;
            overflow-y: auto;
        }
        .table th {
            position: sticky;
            top: 0;
            background: white;
            z-index: 1;
        }
    </style>
</head>
<body>
    <!-- Include Topbar -->
    <div th:replace="fragments/admin-topbar :: admin-topbar"></div>

    <div class="container my-4">
        <div class="row">
            <!-- Include Sidebar -->
            <div class="col-lg-3 mb-4">
                <div th:replace="fragments/admin-sidebar :: admin-sidebar"></div>
            </div>

            <!-- Main Content -->
            <div class="col-lg-9">
                <!-- Page Header -->
                <div class="account-container">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2 class="section-title mb-0">Consolidated Platform Reports</h2>
                        <div class="d-flex gap-2">
                            <button class="btn btn-outline-primary btn-sm" onclick="exportReport()">
                                <i class="fas fa-file-csv"></i> Export to CSV
                            </button>
                            <button class="btn btn-primary btn-sm" onclick="refreshData()">
                                <i class="fas fa-sync-alt"></i> Refresh
                            </button>
                        </div>
                    </div>

                    <!-- Filters -->
                    <div class="filter-card">
                        <form method="get" th:action="@{/admin/consolidated-reports}">
                            <div class="row g-3">
                                <div class="col-md-4">
                                    <label for="period" class="form-label">Period</label>
                                    <select class="form-select" id="period" name="period" th:value="${period}">
                                        <option value="daily" th:selected="${period == 'daily'}">Daily</option>
                                        <option value="weekly" th:selected="${period == 'weekly'}">Weekly</option>
                                        <option value="monthly" th:selected="${period == 'monthly'}">Monthly</option>
                                        <option value="yearly" th:selected="${period == 'yearly'}">Yearly</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="startDate" class="form-label">Start Date</label>
                                    <input type="date" class="form-control" id="startDate" name="startDate" th:value="${startDate}">
                                </div>
                                <div class="col-md-3">
                                    <label for="endDate" class="form-label">End Date</label>
                                    <input type="date" class="form-control" id="endDate" name="endDate" th:value="${endDate}">
                                </div>
                                <div class="col-md-2 d-flex align-items-end">
                                    <button type="submit" class="btn btn-filter w-100">
                                        <i class="fas fa-filter"></i> Apply
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- Key Metrics Grid -->
                    <div class="metric-grid">
                        <!-- Orders Metrics -->
                        <div class="stat-card orders">
                            <h4>Total Orders</h4>
                            <p th:text="${orders?.totalOrders ?: '0'}">0</p>
                            <div class="trend">
                                <i class="fas fa-shopping-cart"></i>
                                <span>In Period</span>
                            </div>
                            <i class="fas fa-receipt"></i>
                        </div>

                        <!-- Average Order Value -->
                        <div class="stat-card orders">
                            <h4>Average Order Value</h4>
                            <p th:text="${orders?.averageOrderValue != null ? (#numbers.formatDecimal(orders.averageOrderValue, 0, 'COMMA', 0, 'POINT') + ' đ') : '0 đ'}">0 đ</p>
                            <div class="trend">
                                <i class="fas fa-coins"></i>
                                <span>Per Order</span>
                            </div>
                            <i class="fas fa-chart-line"></i>
                        </div>

                        <!-- New Users -->
                        <div class="stat-card users">
                            <h4>New User Registrations</h4>
                            <p th:text="${users?.newUsers ?: '0'}">0</p>
                            <div class="trend">
                                <i class="fas fa-user-plus"></i>
                                <span>In Period</span>
                            </div>
                            <i class="fas fa-users"></i>
                        </div>

                        <!-- New Products -->
                        <div class="stat-card products">
                            <h4>New Product Listings</h4>
                            <p th:text="${products?.newProducts ?: '0'}">0</p>
                            <div class="trend">
                                <i class="fas fa-plus"></i>
                                <span>In Period</span>
                            </div>
                            <i class="fas fa-book"></i>
                        </div>

                        <!-- Forum Posts -->
                        <div class="stat-card forum">
                            <h4>New Forum Posts</h4>
                            <p th:text="${forum?.newPosts ?: '0'}">0</p>
                            <div class="trend">
                                <i class="fas fa-edit"></i>
                                <span>In Period</span>
                            </div>
                            <i class="fas fa-comments"></i>
                        </div>

                        <!-- Platform Revenue -->
                        <div class="stat-card revenue">
                            <h4>Platform Revenue</h4>
                            <p th:text="${revenue?.totalRevenue != null ? (#numbers.formatDecimal(revenue.totalRevenue, 0, 'COMMA', 0, 'POINT') + ' đ') : '0 đ'}">0 đ</p>
                            <div class="trend">
                                <i class="fas fa-coins"></i>
                                <span>Commission Fees</span>
                            </div>
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                    </div>
                </div>

                <!-- Charts Grid -->
                <div class="chart-grid">
                    <!-- Orders Trend Chart -->
                    <div class="account-container">
                        <div class="overview-header">
                            <h3 class="section-title mb-0">Orders Trend</h3>
                            <span class="period-badge" th:text="${#strings.capitalize(period)} + ' View'">Monthly View</span>
                        </div>
                        <div class="chart-container">
                            <canvas id="ordersTrendChart"></canvas>
                        </div>
                    </div>

                    <!-- User Registrations Chart -->
                    <div class="account-container">
                        <div class="overview-header">
                            <h3 class="section-title mb-0">User Registrations</h3>
                            <span class="period-badge" th:text="${#strings.capitalize(period)} + ' View'">Monthly View</span>
                        </div>
                        <div class="chart-container">
                            <canvas id="userRegistrationsChart"></canvas>
                        </div>
                    </div>

                    <!-- Product Listings Chart -->
                    <div class="account-container">
                        <div class="overview-header">
                            <h3 class="section-title mb-0">Product Listings</h3>
                            <span class="period-badge" th:text="${#strings.capitalize(period)} + ' View'">Monthly View</span>
                        </div>
                        <div class="chart-container">
                            <canvas id="productListingsChart"></canvas>
                        </div>
                    </div>

                    <!-- Forum Activity Chart -->
                    <div class="account-container">
                        <div class="overview-header">
                            <h3 class="section-title mb-0">Forum Activity</h3>
                            <span class="period-badge" th:text="${#strings.capitalize(period)} + ' View'">Monthly View</span>
                        </div>
                        <div class="chart-container">
                            <canvas id="forumActivityChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Additional Statistics -->
                <div class="account-container">
                    <h3 class="section-title">Platform Overview</h3>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Metric</th>
                                            <th>Total</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><i class="fas fa-users text-primary"></i> Total Users</td>
                                            <td th:text="${platform?.totalUsers ?: '0'}">0</td>
                                        </tr>
                                        <tr>
                                            <td><i class="fas fa-user-tie text-success"></i> Total Buyers</td>
                                            <td th:text="${users?.totalBuyers ?: '0'}">0</td>
                                        </tr>
                                        <tr>
                                            <td><i class="fas fa-store text-warning"></i> Total Sellers</td>
                                            <td th:text="${users?.totalSellers ?: '0'}">0</td>
                                        </tr>
                                        <tr>
                                            <td><i class="fas fa-shop text-info"></i> Active Shops</td>
                                            <td th:text="${platform?.activeShops ?: '0'}">0</td>
                                        </tr>
                                        <tr>
                                            <td><i class="fas fa-book text-danger"></i> Total Products</td>
                                            <td th:text="${platform?.totalProducts ?: '0'}">0</td>
                                        </tr>
                                        <tr>
                                            <td><i class="fas fa-blog text-secondary"></i> Total Blog Posts</td>
                                            <td th:text="${platform?.totalBlogs ?: '0'}">0</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Activity Metric</th>
                                            <th>Count</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><i class="fas fa-eye text-primary"></i> Total Blog Views</td>
                                            <td th:text="${platform?.totalBlogViews ?: '0'}">0</td>
                                        </tr>
                                        <tr>
                                            <td><i class="fas fa-comments text-success"></i> New Comments</td>
                                            <td th:text="${forum?.newComments ?: '0'}">0</td>
                                        </tr>
                                        <tr>
                                            <td><i class="fas fa-user-friends text-warning"></i> Active Forum Users</td>
                                            <td th:text="${forum?.active_users ?: '0'}">0</td>
                                        </tr>
                                        <tr>
                                            <td><i class="fas fa-chart-line text-info"></i> Total Order Value</td>
                                            <td th:text="${orders?.totalOrderValue != null ? (#numbers.formatDecimal(orders.totalOrderValue, 0, 'COMMA', 0, 'POINT') + ' đ') : '0 đ'}">0 đ</td>
                                        </tr>
                                        <tr>
                                            <td><i class="fas fa-percentage text-danger"></i> Platform Commission</td>
                                            <td th:text="${revenue?.totalRevenue != null ? (#numbers.formatDecimal(revenue.totalRevenue, 0, 'COMMA', 0, 'POINT') + ' đ') : '0 đ'}">0 đ</td>
                                        </tr>
                                        <tr>
                                            <td><i class="fas fa-calendar text-secondary"></i> Report Period</td>
                                            <td>
                                                <span th:text="${startDate}">2024-01-01</span> to
                                                <span th:text="${endDate}">2024-12-31</span>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Category Statistics (if available) -->
                <div class="account-container" th:if="${products?.categoryStats != null and !products.categoryStats.empty}">
                    <h3 class="section-title">Product Categories Performance</h3>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Category</th>
                                    <th>Products</th>
                                    <th>Avg Price</th>
                                    <th>Total Views</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr th:each="category : ${products.categoryStats}">
                                    <td th:text="${category.category_name}">Category Name</td>
                                    <td th:text="${category.product_count}">0</td>
                                    <td th:text="${category.avg_price != null ? #numbers.formatCurrency(category.avg_price) : '$0'}">$0</td>
                                    <td th:text="${category.total_views ?: '0'}">0</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Chart Initialization -->
    <script th:inline="javascript">
        // Orders Trend Chart
        const ordersTrendCtx = document.getElementById('ordersTrendChart').getContext('2d');
        const ordersLabelsJson = /*[[${orders?.trendLabels}]]*/ '[]';
        const ordersLabels = JSON.parse(ordersLabelsJson);
        const ordersDataJson = /*[[${orders?.trendData}]]*/ '[]';
        const ordersData = JSON.parse(ordersDataJson);

        new Chart(ordersTrendCtx, {
            type: 'line',
            data: {
                labels: ordersLabels,
                datasets: [{
                    label: 'Orders',
                    data: ordersData,
                    borderColor: '#11998e',
                    backgroundColor: 'rgba(17, 153, 142, 0.1)',
                    tension: 0.4,
                    fill: true,
                    pointBackgroundColor: '#11998e',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 5
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'top'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Number of Orders'
                        }
                    }
                }
            }
        });

        // User Registrations Chart
        const userRegistrationsCtx = document.getElementById('userRegistrationsChart').getContext('2d');
        const usersLabelsJson = /*[[${users?.trendLabels}]]*/ '[]';
        const usersLabels = JSON.parse(usersLabelsJson);
        const usersDataJson = /*[[${users?.trendData}]]*/ '[]';
        const usersData = JSON.parse(usersDataJson);

        new Chart(userRegistrationsCtx, {
            type: 'bar',
            data: {
                labels: usersLabels,
                datasets: [{
                    label: 'New Users',
                    data: usersData,
                    backgroundColor: 'rgba(102, 126, 234, 0.8)',
                    borderColor: '#667eea',
                    borderWidth: 2,
                    borderRadius: 5
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'top'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Number of Users'
                        }
                    }
                }
            }
        });

        // Product Listings Chart
        const productListingsCtx = document.getElementById('productListingsChart').getContext('2d');
        const productsLabelsJson = /*[[${products?.trendLabels}]]*/ '[]';
        const productsLabels = JSON.parse(productsLabelsJson);
        const productsDataJson = /*[[${products?.trendData}]]*/ '[]';
        const productsData = JSON.parse(productsDataJson);

        new Chart(productListingsCtx, {
            type: 'bar',
            data: {
                labels: productsLabels,
                datasets: [{
                    label: 'New Products',
                    data: productsData,
                    backgroundColor: 'rgba(240, 147, 251, 0.8)',
                    borderColor: '#f093fb',
                    borderWidth: 2,
                    borderRadius: 5
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'top'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Number of Products'
                        }
                    }
                }
            }
        });

        // Forum Activity Chart (Combined Posts and Comments)
        const forumActivityCtx = document.getElementById('forumActivityChart').getContext('2d');
        const blogLabelsJson = /*[[${forum?.blogTrendLabels}]]*/ '[]';
        const blogLabels = JSON.parse(blogLabelsJson);
        const blogDataJson = /*[[${forum?.blogTrendData}]]*/ '[]';
        const blogData = JSON.parse(blogDataJson);
        const commentDataJson = /*[[${forum?.commentTrendData}]]*/ '[]';
        const commentData = JSON.parse(commentDataJson);

        new Chart(forumActivityCtx, {
            type: 'line',
            data: {
                labels: blogLabels,
                datasets: [{
                    label: 'New Posts',
                    data: blogData,
                    borderColor: '#4facfe',
                    backgroundColor: 'rgba(79, 172, 254, 0.1)',
                    tension: 0.4,
                    fill: false,
                    pointBackgroundColor: '#4facfe',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 4
                }, {
                    label: 'New Comments',
                    data: commentData,
                    borderColor: '#00f2fe',
                    backgroundColor: 'rgba(0, 242, 254, 0.1)',
                    tension: 0.4,
                    fill: false,
                    pointBackgroundColor: '#00f2fe',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'top'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Activity Count'
                        }
                    }
                }
            }
        });

        // Utility functions
        function exportReport() {
            // Show loading indicator
            const loadingToast = showToast('Preparing CSV export...', 'info');

            try {
                const params = new URLSearchParams(window.location.search);
                const exportUrl = '/admin/consolidated-reports/export-csv?' + params.toString();
                const filename = `consolidated_reports_${new Date().toISOString().split('T')[0]}.csv`;

                // Create a temporary link to trigger download
                const link = document.createElement('a');
                link.href = exportUrl;
                link.download = filename;
                link.style.display = 'none';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                // Hide loading and show success
                setTimeout(() => {
                    hideToast(loadingToast);
                    showToast(`CSV export completed! File: ${filename}`, 'success');
                }, 1000);

            } catch (error) {
                console.error('Export error:', error);
                hideToast(loadingToast);
                showToast('CSV export failed. Please try again.', 'error');
            }
        }

        function refreshData() {
            window.location.reload();
        }

        // Toast notification functions
        function showToast(message, type = 'info') {
            const toastContainer = getOrCreateToastContainer();
            const toastId = 'toast-' + Date.now();

            const toastHtml = `
                <div id="${toastId}" class="toast align-items-center text-white bg-${getBootstrapColor(type)} border-0" role="alert" aria-live="assertive" aria-atomic="true">
                    <div class="d-flex">
                        <div class="toast-body">
                            <i class="fas ${getToastIcon(type)} me-2"></i>
                            ${message}
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                    </div>
                </div>
            `;

            toastContainer.insertAdjacentHTML('beforeend', toastHtml);
            const toastElement = document.getElementById(toastId);
            const toast = new bootstrap.Toast(toastElement, {
                autohide: type !== 'info', // Keep info toasts visible until manually closed
                delay: type === 'error' ? 5000 : 3000
            });

            toast.show();

            // Auto-remove from DOM after hiding
            toastElement.addEventListener('hidden.bs.toast', () => {
                toastElement.remove();
            });

            return toastId;
        }

        function hideToast(toastId) {
            const toastElement = document.getElementById(toastId);
            if (toastElement) {
                const toast = bootstrap.Toast.getInstance(toastElement);
                if (toast) {
                    toast.hide();
                }
            }
        }

        function getOrCreateToastContainer() {
            let container = document.getElementById('toast-container');
            if (!container) {
                container = document.createElement('div');
                container.id = 'toast-container';
                container.className = 'toast-container position-fixed top-0 end-0 p-3';
                container.style.zIndex = '1055';
                document.body.appendChild(container);
            }
            return container;
        }

        function getBootstrapColor(type) {
            switch (type) {
                case 'success': return 'success';
                case 'error': return 'danger';
                case 'warning': return 'warning';
                case 'info': return 'info';
                default: return 'primary';
            }
        }

        function getToastIcon(type) {
            switch (type) {
                case 'success': return 'fa-check-circle';
                case 'error': return 'fa-exclamation-circle';
                case 'warning': return 'fa-exclamation-triangle';
                case 'info': return 'fa-info-circle';
                default: return 'fa-bell';
            }
        }
    </script>
</body>
</html>
