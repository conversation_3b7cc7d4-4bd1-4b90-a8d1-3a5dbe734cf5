<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Delete Account - Bookix</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Lora:wght@400;700&family=Montserrat:wght@400;700&family=Open+Sans:wght@400;600&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" th:href="@{/css/style.css}">

    <style>
        /* Các style tùy chỉnh cho trang này */
        .consequences ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .consequences li {
            background-color: #f8f9fa;
            padding: 12px 15px;
            margin-bottom: 10px;
            border-radius: 4px;
            font-size: 14px;
            line-height: 1.6;
            border-left: 3px solid #007bff;
        }
        .consequences li strong {
            color: #d9534f;
        }
        .notice {
            background-color: #ffebee;
            color: #c62828;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            font-weight: bold;
            font-size: 15px;
        }
        .confirmation-group {
            margin-top: 25px;
            margin-bottom: 25px;
        }
        .submit-btn {
            background-color: #d9534f; /* Màu đỏ cho hành động xóa */
            border-color: #d9534f;
        }
        .submit-btn:hover {
            background-color: #c9302c;
            border-color: #c9302c;
        }
        .submit-btn:disabled {
            background-color: #f8d7da; /* Màu đỏ nhạt khi bị vô hiệu hóa */
            border-color: #f8d7da;
            cursor: not-allowed;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            font-weight: bold;
            margin-bottom: 8px;
        }
        .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
    </style>
</head>
<body>

<!-- Sử dụng chung header fragment -->
<div th:replace="~{fragments/header :: header-content}"></div>

<main class="account-page py-5 bg-light">
    <div class="container">
        <nav aria-label="breadcrumb mb-4">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a th:href="@{/}">Home</a></li>
                <li class="breadcrumb-item"><a th:href="@{/buyer/account-info}">Account Information</a></li>
                <li class="breadcrumb-item active" aria-current="page">Delete Account</li>
            </ol>
        </nav>

        <div class="row">
            <!-- Sử dụng chung sidebar fragment, đặt tab 'delete' là active -->
            <div th:replace="~{fragments/buyer-account-sidebar :: sidebar('delete')}"></div>

            <!-- Nội dung chính -->
            <section class="col-lg-9 account-content">
                <div class="card">
                    <div class="card-header bg-white py-3">
                    </div>
                    <div class="card-body p-4">
                        <h2 class="card-title text-danger mb-4"><i class="fas fa-exclamation-triangle me-2"></i> Delete Account Permanently</h2>

                        <div th:if="${error}" class="alert alert-danger" role="alert">
                            <span th:text="${error}"></span>
                        </div>
                        <div th:if="${success}" class="alert alert-success" role="alert">
                            <span th:text="${success}"></span>
                        </div>

                        <div class="mb-3">
                            <label for="reason" class="form-label fw-bold">Reason for Deletion</label>
                            <textarea class="form-control" id="reason" name="reason" rows="3" required></textarea>
                        </div>

                        <p class="text-muted mb-4">You are about to permanently delete your Bookix account. This action is irreversible.</p>

                        <div class="notice">
                            <i class="fas fa-info-circle me-2"></i> Please read the following warnings and consequences carefully before proceeding.
                        </div>

                        <div class="consequences mb-4">
                            <ul>
                                <li>You will <strong>permanently lose</strong> all your data, including your purchase history, reviews, wishlists, and any other items associated with your Bookix account.</li>
                                <li>Ensure your account has <strong>no pending orders</strong> or unresolved complaints. Active orders or complaints must be resolved before your account can be deleted.</li>
                                <li>Bookix may retain some transaction or activity data for a limited period for auditing and legal purposes after successful deletion, as outlined in our Privacy Policy.</li>
                                <li>After successful deletion, you will <strong>no longer be able to log in</strong> or access the deleted account.</li>
                                <li>The email address and phone number linked to the deleted account <strong>cannot be used</strong> to register a new Bookix account or link to another existing account for a certain period.</li>
                                <li>If this account is used to access any associated services (e.g., seller dashboard if you are a seller), access to those services will also be <strong>revoked</strong>.</li>
                            </ul>
                        </div>

                        <div class="notice">
                            <i class="fas fa-info-circle me-2"></i> Please note that if you have any active orders or complaints, you will not be able to delete your account until they are resolved.
                        </div>

                       

                        <p class="mb-3">To proceed with your account deletion request, please re-enter your password to confirm your identity.</p>

                        <form th:action="@{/account-delete-request}" method="post">
                            <div class="mb-3">
                                <label for="password" class="form-label fw-bold">Your Password</label>
                                <input type="password" class="form-control" id="password" name="password" required>
                            </div>

                            <div class="form-check confirmation-group">
                                <input class="form-check-input" type="checkbox" id="confirm-delete" name="confirmDelete" required>
                                <label class="form-check-label" for="confirm-delete">
                                    I understand the consequences and wish to proceed with deleting my Bookix account.
                                </label>
                            </div>

                            <button type="submit" class="btn btn-danger w-100 fw-bold py-2 submit-btn" id="submitDeleteBtn" disabled>Request Account Deletion</button>
                        </form>
                    </div>
                </div>
            </section>
        </div>
    </div>
</main>

<!-- Sử dụng chung footer fragments -->
<div th:replace="~{fragments/newsletter :: newsletter-section}"></div>
<div th:replace="~{fragments/footer :: footer-content}"></div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
    const confirmCheckbox = document.getElementById('confirm-delete');
    const submitButton = document.getElementById('submitDeleteBtn');

    confirmCheckbox.addEventListener('change', function() {
        submitButton.disabled = !this.checked;
    });
</script>

</body>
</html>
