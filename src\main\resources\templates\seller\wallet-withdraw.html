<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Withdraw Funds - ReadHub Seller</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Lora:wght@400;700&family=Montserrat:wght@400;700&family=Open+Sans:wght@400;600&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" th:href="@{/css/seller-style.css}">

    <style>
        .wallet-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .wallet-balance {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .withdrawal-form {
            background: white;
            border-radius: 10px;
            padding: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .form-label {
            font-weight: 600;
            color: #333;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .btn-withdraw {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            padding: 12px 30px;
            font-weight: 600;
        }

        .btn-withdraw:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            transform: translateY(-1px);
        }

        .withdrawal-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
        }

        .info-item:last-child {
            margin-bottom: 0;
        }
    </style>
</head>
<body>

<div class="topbar-wrapper" th:replace="fragments/seller-topbar :: seller-topbar"></div>

<div class="container-fluid">
    <div class="row">
        <div class="col-lg-3 mb-4 sidebar-wrapper">
            <div th:replace="fragments/seller-sidebar :: seller-sidebar"></div>
        </div>

        <div class="col-lg-9 main-content-wrapper">
            <main class="py-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h3 class="section-title mb-0">
                        <i class="fas fa-money-bill-wave text-warning me-2"></i>Withdraw Funds
                    </h3>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item"><a th:href="@{/seller/dashboard}">Dashboard</a></li>
                            <li class="breadcrumb-item"><a th:href="@{/seller/wallet}">My Wallet</a></li>
                            <li class="breadcrumb-item active">Withdraw Funds</li>
                        </ol>
                    </nav>
                </div>

                <!-- Error Message -->
                <div th:if="${errorMessage}" class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <span th:text="${errorMessage}"></span>
                </div>

                <!-- Current Balance Card -->
                <div class="wallet-card">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h3 class="mb-2">
                                <i class="fas fa-wallet me-2"></i>
                                Current Balance
                            </h3>
                            <div class="wallet-balance" th:text="${walletBalance.formattedBalance}">0 VND</div>
                            <p class="mb-0 opacity-75">Available for withdrawal</p>
                        </div>
                        <div class="col-md-4 text-md-end">
                            <a th:href="@{/seller/wallet}" class="btn btn-light btn-sm">
                                <i class="fas fa-arrow-left me-1"></i> Back to Wallet
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Withdrawal Form -->
                <div class="withdrawal-form">
                    <h4 class="mb-4">
                        <i class="fas fa-money-bill-wave me-2"></i>
                        Withdraw Funds
                    </h4>

                    <!-- Withdrawal Information -->
                    <div class="withdrawal-info">
                        <h6 class="mb-3"><i class="fas fa-info-circle me-2"></i>Withdrawal Information</h6>
                        <div class="info-item">
                            <span>Minimum withdrawal:</span>
                            <strong>10,000 VND</strong>
                        </div>
                        <div class="info-item">
                            <span>Maximum withdrawal:</span>
                            <strong th:text="${#numbers.formatDecimal(walletBalance.balance, 0, 'COMMA', 0, 'POINT')} + ' VND (Current Balance)'">0 VND</strong>
                        </div>
                        <div class="info-item">
                            <span>Processing time:</span>
                            <strong>1-3 business days</strong>
                        </div>
                    </div>

                    <form th:action="@{/seller/wallet/withdraw}" th:object="${withdrawalRequest}" method="post">
                        <div class="row">
                            <!-- Amount -->
                            <div class="col-md-6 mb-3">
                                <label for="amount" class="form-label">
                                    <i class="fas fa-dollar-sign me-1"></i>
                                    Withdrawal Amount (VND) *
                                </label>
                                <input type="number"
                                       class="form-control"
                                       id="amount"
                                       th:field="*{amount}"
                                       min="10000"
                                       th:max="${walletBalance.balance}"
                                       step="1000"
                                       th:placeholder="'Enter amount (max: ' + ${#numbers.formatDecimal(walletBalance.balance, 0, 'COMMA', 0, 'POINT')} + ' VND)'">
                                <div th:if="${#fields.hasErrors('amount')}" class="text-danger small mt-1">
                                    <span th:errors="*{amount}"></span>
                                </div>
                            </div>

                            <!-- Bank Name -->
                            <div class="col-md-6 mb-3">
                                <label for="bankName" class="form-label">
                                    <i class="fas fa-university me-1"></i>
                                    Bank Name *
                                </label>
                                <input type="text"
                                       class="form-control"
                                       id="bankName"
                                       th:field="*{bankName}"
                                       placeholder="e.g., Vietcombank, BIDV, Techcombank">
                                <div th:if="${#fields.hasErrors('bankName')}" class="text-danger small mt-1">
                                    <span th:errors="*{bankName}"></span>
                                </div>
                            </div>

                            <!-- Account Number -->
                            <div class="col-md-6 mb-3">
                                <label for="accountNumber" class="form-label">
                                    <i class="fas fa-credit-card me-1"></i>
                                    Account Number *
                                </label>
                                <input type="text"
                                       class="form-control"
                                       id="accountNumber"
                                       th:field="*{accountNumber}"
                                       pattern="[0-9]{6,20}"
                                       placeholder="Enter your bank account number">
                                <div th:if="${#fields.hasErrors('accountNumber')}" class="text-danger small mt-1">
                                    <span th:errors="*{accountNumber}"></span>
                                </div>
                            </div>

                            <!-- Account Holder Name -->
                            <div class="col-md-6 mb-3">
                                <label for="accountHolderName" class="form-label">
                                    <i class="fas fa-user me-1"></i>
                                    Account Holder Name *
                                </label>
                                <input type="text"
                                       class="form-control"
                                       id="accountHolderName"
                                       th:field="*{accountHolderName}"
                                       placeholder="Full name as on bank account">
                                <div th:if="${#fields.hasErrors('accountHolderName')}" class="text-danger small mt-1">
                                    <span th:errors="*{accountHolderName}"></span>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <button type="submit" class="btn btn-withdraw text-white me-3">
                                    <i class="fas fa-paper-plane me-2"></i>
                                    Submit Withdrawal Request
                                </button>
                                <a th:href="@{/seller/wallet}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-2"></i>
                                    Cancel
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </main>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

<script>
    // Balance validation is handled by HTML5 max attribute
    // Additional client-side validation for better UX
    document.getElementById('amount').addEventListener('input', function(e) {
        const amount = parseFloat(e.target.value) || 0;
        const maxBalance = parseFloat(e.target.getAttribute('max')) || 0;

        // Clear custom validity
        e.target.setCustomValidity('');

        // Check if amount exceeds balance
        if (amount > maxBalance) {
            e.target.setCustomValidity('Amount exceeds your current balance');
        }
    });
</script>

</body>
</html>
